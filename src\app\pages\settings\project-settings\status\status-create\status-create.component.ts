


import { Component, EventEmitter, Output, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
// import { FormsModule} from '@angular/forms';
import { ActivatedRoute} from '@angular/router';
import { CommonModule } from '@angular/common';
import { Status, StatusService } from '../../../../../services/status.service';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
@Component({
  selector: 'app-status-create',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule,MatIconModule,RouterModule],
  templateUrl: './status-create.component.html',
  styleUrls: ['./status-create.component.scss']
})
export class StatusCreateComponent  {
  private fb = inject(FormBuilder);
  private statusService = inject(StatusService);
  // Emits event when form is submitted
  @Output() formSubmitted = new EventEmitter<void>();
  // Emits event when form is closed
  @Output() closeForm = new EventEmitter<void>();
  statusForm!: FormGroup;
  statusOptions = ['New', 'In Progress', 'Resolved', 'Closed'];
  isDragging!: boolean;
  isEditMode: any;
  statusid: any;
  status : any
  duplicateStatus: boolean = false;

  constructor(private router: Router,    private route: ActivatedRoute,) {
    this.statusForm = this.fb.group({
      statusname: ['', Validators.required]
    });
  }
  
ngOnInit(): void {
  this.initForm();
  this.statusForm.get('statusname')?.valueChanges.subscribe(() => {
    this.duplicateStatusError = false; // Reset error on input change
  });

  // Detect route changes (Create vs Edit)
  this.route.paramMap.subscribe(params => {
    const id = params.get('id');
    if (id) {
      this.isEditMode = true;
      this.statusid = +id; // Convert string to number
      this.loadStatusDetails(this.statusid);
    } else {
      this.isEditMode = false;
    }
  });
}
initForm(): void {
  this.statusForm = this.fb.group({
    // statusId: ['', Validators.required],
    statusname: ['', Validators.required]
  });
}
loadStatusDetails(id: number): void {
  this.statusService.getStatus(id).subscribe(status => {
    if (status) {
      this.statusForm.patchValue({
        // statusId: status.statusId,
        statusname: status.statusname
      });
    }
  });
}
 duplicateStatusError: boolean = false;

  goBack(): void {

    this.router.navigate(['/settings/project/status']);

  }
  // two in one 
  onStatusSubmit(): void {
    if (this.statusForm.invalid) {
      return;
    }

    this.duplicateStatusError = false;

    const statusData: Status = {
      statusid: this.statusid,
      statusname: this.statusForm.value.statusname
    };

    const serviceCall = this.isEditMode
      ? this.statusService.updateStatus(this.statusid, statusData)
      : this.statusService.addStatus(statusData);

    serviceCall.subscribe({
      next: () => {
        this.router.navigate(['/settings/project/status']); // Consistent navigation
      },
      error: (error) => {
        console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} status:`, error);
        if (error.status === 409) {
          this.duplicateStatusError = true;
        } else if (error.status === 400) {
          alert(error.error?.message || "Status name cannot be empty.");
        } else {
          alert(error.error?.message || `Failed to ${this.isEditMode ? 'update' : 'create'} status.`);
        }
      }
    });
  }
}


