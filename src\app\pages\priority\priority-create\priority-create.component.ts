import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { Priority, PriorityService } from '../../../services/priority.service';



@Component({
  selector: 'app-priority-create',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatIconModule],
  templateUrl: './priority-create.component.html',
  styleUrls: ['./priority-create.component.css'],
})
export class PriorityCreateComponent implements OnInit {
  priorityForm!: FormGroup;
  isEditMode = false;
  priorityId!: number;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private priorityService: PriorityService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.priorityForm = this.fb.group({
      priorityName: ['', Validators.required],
      isActive: [true]  // Default: true
    });

    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.isEditMode = true;
        this.priorityId = +params['id'];
        this.loadPriority(this.priorityId);
      }
    });
  }

 loadPriority(id: number): void {
  this.priorityService.getPriorityById(id).subscribe((priority: Priority) => {
    this.priorityForm.patchValue({
      priorityName: priority.priorityName || '',
      isActive: priority.isActive
    });
  });
}

  onSubmit(): void {
    if (this.priorityForm.invalid) return;

    const priority: Priority = {
  priorityId: this.priorityId || 0,
  priorityName: this.priorityForm.value.priorityName,
  isActive: this.priorityForm.value.isActive
};
 this.loading = true;

    if (this.isEditMode) {
      this.priorityService.updatePriority(this.priorityId, priority).subscribe({
        next: () => this.router.navigate(['/settings/project/priority/priority-list']),
        error: (err) => console.error('Error updating priority:', err),
      });
    } else {
      this.priorityService.createPriority(priority).subscribe({
        next: () => this.router.navigate(['/settings/project/priority/priority-list']),
        error: (err) => console.error('Error creating priority:', err),
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/settings/project/priority/priority-list']);
  }
}
