import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { Router } from '@angular/router';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-profile-menu-dialog',
  standalone: true,
  imports: [
    CommonModule, 
    MatDialogModule, 
    MatButtonModule, 
    MatIconModule,
    MatDividerModule
  ],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ transform: 'translateY(-20px)', opacity: 0 }),
        animate('200ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))
      ])
    ])
  ],
  template: `
    <div class="w-[320px] p-4 bg-white rounded-lg shadow-lg" [@slideIn]>
      <!-- Header with user info -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="relative">
          <img src="https://placehold.co/64x64/6366F1/ffffff?text=KD" 
               alt="Profile" 
               class="w-16 h-16 rounded-full">
          <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Kate Dudley</h2>
          <p class="text-sm text-gray-500">Administrator</p>
          <p class="text-sm text-gray-500">kate.dudley&#64;example.com</p>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-3 gap-3 mb-6">
        <button class="flex flex-col items-center justify-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                (click)="navigateTo('/settings/profile')">
          <mat-icon class="text-gray-600 mb-1">account_circle</mat-icon>
          <span class="text-xs text-gray-600">Profile</span>
        </button>
        <button class="flex flex-col items-center justify-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                (click)="navigateTo('/settings')">
          <mat-icon class="text-gray-600 mb-1">settings</mat-icon>
          <span class="text-xs text-gray-600">Settings</span>
        </button>
        <button class="flex flex-col items-center justify-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                (click)="navigateTo('/notifications')">
          <mat-icon class="text-gray-600 mb-1">notifications</mat-icon>
          <span class="text-xs text-gray-600">Alerts</span>
        </button>
      </div>

      <!-- Menu Items -->
      <div class="space-y-1">
        <button mat-button 
                class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                (click)="navigateTo('/dashboard')">
          <mat-icon class="!mr-3 text-gray-500">dashboard</mat-icon>
          <span class="text-gray-700">Dashboard</span>
        </button>

        <button mat-button 
                class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                (click)="navigateTo('/tickets/list')">
          <mat-icon class="!mr-3 text-gray-500">confirmation_number</mat-icon>
          <span class="text-gray-700">My Tickets</span>
        </button>

        <button mat-button 
                class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                (click)="navigateTo('/settings/profile')">
          <mat-icon class="!mr-3 text-gray-500">account_box</mat-icon>
          <span class="text-gray-700">Account Settings</span>
        </button>

        <button mat-button 
                class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                (click)="navigateTo('/help')">
          <mat-icon class="!mr-3 text-gray-500">help</mat-icon>
          <span class="text-gray-700">Help Center</span>
        </button>
      </div>

      <mat-divider class="my-4"></mat-divider>

      <!-- Logout Button -->
      <button mat-flat-button 
              color="warn"
              class="w-full !py-2 !px-4 !rounded-lg"
              (click)="logout()">
        <mat-icon class="!mr-2">logout</mat-icon>
        Sign Out
      </button>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }

    :host ::ng-deep .mat-mdc-dialog-container {
      padding: 0 !important;
    }

    .mat-divider {
      margin: 1rem 0 !important;
    }

    :host ::ng-deep {
      .mat-mdc-button {
        height: 44px !important;
      }

      .mat-mdc-flat-button {
        height: 44px !important;
      }
    }
  `]
})
export class ProfileMenuDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ProfileMenuDialogComponent>,
    private router: Router
  ) {}

  navigateTo(route: string): void {
    this.dialogRef.close();
    this.router.navigate([route]);
  }

  logout(): void {
    // Implement logout logic here
    this.dialogRef.close();
    this.router.navigate(['/']);
  }
}