import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Sprint, SprintStatus } from '../Models/sprint.model';

@Injectable({
  providedIn: 'root'
})
export class SprintService {
  private apiUrl = 'http://localhost:3000/api/Sprint'; // Update with actual API URL

  constructor(private http: HttpClient) {}

  getSprints(): Observable<Sprint[]> {
    return this.http.get<Sprint[]>(`${this.apiUrl}`);
  }

  getSprintById(id: number): Observable<Sprint> {
    return this.http.get<Sprint>(`${this.apiUrl}/${id}`);
  }

  createSprint(sprint: Sprint): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}`, sprint);
  }

  updateSprint(id: number, sprint: Sprint): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, sprint);
  }

  // deleteSprint(id: number): Observable<void> {
  //   return this.http.delete<void>(`${this.apiUrl}/${id}`);
  // }

canDeleteSprint(id: number): Observable<{ canDelete: boolean }> {
  return this.http.get<{ canDelete: boolean }>(`${this.apiUrl}/${id}/can-delete`);
}

deleteSprint(id: number): Observable<any> {
  return this.http.delete(`${this.apiUrl}/${id}`);
}



  getStatuses(): Observable<SprintStatus[]> {
    return this.http.get<SprintStatus[]>('http://localhost:3000/api/sprint-status');
  }
}
