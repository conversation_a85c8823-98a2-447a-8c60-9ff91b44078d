import { Component,OnInit ,ViewChild} from '@angular/core';
import { TagService } from '../../../../../services/tag.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatTableDataSource } from '@angular/material/table';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { MatDialog,MatDialogRef } from '@angular/material/dialog';
import { TemplateRef } from '@angular/core';
import { MatPaginator, MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { trigger, transition, style, animate } from '@angular/animations';
import { Tag } from '../../../../../Models/tag.model';



@Component({
  selector: 'app-list-tag',
  imports: [CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatInputModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    MatSelectModule,
    FormsModule,MatDialogModule
   
           
            
  ],
  templateUrl: './list-tag.component.html',
  styleUrl: './list-tag.component.css',
    animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ])
    ])
  ]
})



export class ListTagComponent implements OnInit {
   
 tags: Tag[] = [];
  paginatedTags: Tag[] = [];
  pageSizeOptions: number[] = [5, 7, 10, 25];
  pageSize: number = 5;
  currentPage: number = 0;
  totalRecords: number = 0;
  Math = Math;
  successMessage: string | null = null;
  errorMessage: string | null = null;

  selectedTagId!: number;
  selectedTagName!: string;
  currentDialogRef: any; // or MatDialogRef<any>


  isForeignKeyConstraint = false;  // To track which dialog to show

  @ViewChild('deleteDialog') deleteDialog!: TemplateRef<any>;
  @ViewChild('fkConstraintDialog') fkConstraintDialog!: TemplateRef<any>;

  constructor(
    private tagService: TagService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadTags();
  }
loadTags(): void {
  this.tagService.getAllTags().subscribe(data => {
    this.tags = data;
    this.totalRecords = data.length;

    //this.tags.sort((a, b) => a.tag_id - b.tag_id);

    const maxPage = Math.ceil(this.totalRecords / this.pageSize);

    // ⚠️ Adjust current page if it goes out of bounds after deletion
    if (this.currentPage >= maxPage && this.currentPage > 0) {
      this.currentPage = maxPage - 1;
    }

    this.updatePaginatedTags();
  });
}


  updatePaginatedTags(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.totalRecords);
    this.paginatedTags = this.tags.slice(startIndex, endIndex);
  }

  handlePageEvent(event: any): void {
    this.pageSize = +event.target.value;
    this.currentPage = 0;
    this.updatePaginatedTags();
  }

  onPageChange(index: number): void {
    this.currentPage = index;
    this.updatePaginatedTags();
  }

  goToFirstPage(): void {
    this.currentPage = 0;
    this.updatePaginatedTags();
  }

  goToLastPage(): void {
    this.currentPage = Math.ceil(this.totalRecords / this.pageSize) - 1;
    this.updatePaginatedTags();
  }

  goToPreviousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updatePaginatedTags();
    }
  }

  goToNextPage(): void {
    if (this.currentPage < Math.ceil(this.totalRecords / this.pageSize) - 1) {
      this.currentPage++;
      this.updatePaginatedTags();
    }
  }

  editTag(id: number): void {
    this.router.navigate(['/settings/project/tag/create-tag'], { queryParams: { id } });
  }

  // Open the appropriate delete dialog based on foreign key constraint
openDeleteDialog(id: number, name: string): void {
  this.selectedTagId = id;
  this.selectedTagName = name;
  this.errorMessage = '';
  this.successMessage = '';

  this.tagService.checkFkConstraint(id).subscribe({
    next: (hasFk: boolean) => {
      if (hasFk) {
        this.dialog.open(this.fkConstraintDialog, { width: '400px', disableClose: true });
      } else {
        this.dialog.open(this.deleteDialog, { width: '400px', disableClose: true });
      }
    },
    error: () => {
      this.errorMessage = 'Error checking foreign key constraint.';
    }
  });
}

confirmDelete(): void {
  this.tagService.deleteTag(this.selectedTagId).subscribe({
    next: () => {
      this.successMessage = 'Tag deleted successfully!';
      this.dialog.closeAll();
      this.loadTags();
      this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
        this.router.navigate(['/settings/project/tag']);
      });
    },
    error: (err) => {
      this.dialog.closeAll();
      this.errorMessage = err.error?.message || 'An unexpected error occurred.';
    }
  });
}

  cancelDelete(): void {
    this.dialog.closeAll();

    // Redirect after cancel or close dialog
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/settings/project/tag']);
    });
  }

   navigateToAddTag(): void {
    this.router.navigate(['/settings/project/tag/create-tag']);
  }
}