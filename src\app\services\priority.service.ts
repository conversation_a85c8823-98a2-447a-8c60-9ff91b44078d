import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';

export interface Priority {
  priorityId: number;
  priorityName: string;
  isActive: boolean; // ✅ Add this line
}

@Injectable({
  providedIn: 'root'
})
export class PriorityService {
  
  private baseUrl = 'http://localhost:3000/api/Priority';

  constructor(private http: HttpClient) {}

  getPriorities(): Observable<Priority[]> {
    return this.http.get<Priority[]>(`${this.baseUrl}/AllPriority`);
  }

  getPriorityById(id: number): Observable<Priority> {
    return this.http.get<Priority>(`${this.baseUrl}/${id}`);
  }
 
  createPriority(priority: Priority): Observable<Priority> {
    return this.http.post<Priority>(`${this.baseUrl}`, priority);
  }

  updatePriority(id: number, priority: Priority): Observable<Priority> {
    return this.http.put<Priority>(`${this.baseUrl}/${id}`, priority);
  }

  deletePriority(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }
  checkFkConstraint(id: number): Observable<boolean> {
  return this.http.get<{ isReferenced: boolean }>(`${this.baseUrl}/check-fk/${id}`)
    .pipe(map((response: { isReferenced: any; }) => response.isReferenced));
}

}
