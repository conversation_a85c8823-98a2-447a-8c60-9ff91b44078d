/* :host {
  display: block;
  height: 100vh;
  overflow-y: auto;
  padding: 2rem;
  background-color: #F9FAFB;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.25rem;
}

.error-message {
  color: red;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.submit-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: transparent;
  color: #1c3faa;
  padding: 8px 16px;
  border: 2px solid transparent;
  border-radius: 30px;
  transition: border-color 0.3s ease, color 0.3s ease;
}

.back-button mat-icon {
  font-size: 20px;
}

.back-button:hover {
  border-color: #8ca7fa;
  color: #1c3faa;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.submit-container button {
  background-color: transparent;
  padding: 8px 16px;
  border: 2px solid transparent;
  border-radius: 30px;
  transition: border-color 0.3s ease, color 0.3s ease;
}

.submit-container button:hover {
  border-color: #8ca7fa;
  color: #1c3faa;
  transform: scale(1.05);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
} */




:host {
  display: block;
  height: 100vh;
  overflow-y: auto;
  padding: 2rem;
  background-color: #F9FAFB;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.25rem;
}

.error-message {
  color: red;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.submit-container {
 
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
}

.back-button {
  
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: transparent;
  color: #1c3faa;
  padding: 8px 16px;
  border: #1D4ED8;
  border-radius: 30px;
  transition: border-color 0.3s ease, color 0.3s ease;
}

.back-button mat-icon {
  
  font-size: 20px;
}

.back-button:hover {
  border-color: #8ca7fa;
  color: #1c3faa;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.submit-container button {
  background-color: transparent;
  padding: 8px 16px;
  border: 2px solid transparent;
  border-radius: 30px;
  transition: border-color 0.3s ease, color 0.3s ease;
}

.submit-container button:hover {
  border-color: #8ca7fa;
  color: #1c3faa;
  transform: scale(1.05);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}




/* Base styles for the button */
button[mat-raised-button] {
  border: 2px solid;
  border-radius: 9999px;
  padding: 10px 24px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  background-color: white;
}

/* Style when form is invalid: black border and text */
.invalid-button {
  
  border-color: black;
  color: black;
}

/* Style when form is valid: blue border and text */
.valid-button {
  border-color: #1D4ED8; /* Blue */
  color: #1D4ED8;
}

/* Optional hover effect */
.valid-button:hover {
  background-color: #1D4ED8;
  color: white;
}

.invalid-button:hover {
  background-color: black;
  color: white;
}