.scroll-container {
    padding: 20px;
  }
  
  .form-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
  }
  
  .form-label {
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .form-input {
    padding: 10px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 16px;
  }
  
  .form-input:focus {
    border-color: #3b82f6;
    outline: none;
  }
  
  .error-message {
    color: #dc2626;
    font-size: 13px;
    margin-top: 4px;
  }
  
  .button-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  
 .right-button {
  background-color: #3b82f6;
  color: white;
  padding: 10px 24px; /* Optional: widen horizontal padding for better pill shape */
  border: none;
  font-weight: 600;
  border-radius: 9999px; /* Pill-shaped */
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 6px;
}

  
  .right-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }
  
  a[routerLink] {
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
  }


.error-message {
  display: block;
  color: red;
  font-size: 13px;
  margin-top: 4px;
}

/* Makes checkbox smaller and styled */
.custom-checkbox {
  width: 20px;
  height: 20px;
  accent-color: #3b82f6; /* Blue color to match theme */
  cursor: pointer;
  margin-top: 1px; /* Adjust alignment */
}

button.back-to-list-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2563EB;             /* solid blue-600 */
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 4px 12px;
  border-radius: 9999px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

button.back-to-list-btn mat-icon {
  font-size: 20px;
  color: #2563EB;             /* same blue */
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1px;
}

button.back-to-list-btn:hover {
  background-color: #EFF6FF;  /* light blue oval */
  color: #1E40AF;             /* slightly darker blue on hover (blue-800) */
}

button.back-to-list-btn:hover mat-icon {
  color: #1E40AF;
}

button.back-to-list-btn:focus-visible {
  outline: 2px solid #2563EB;
  outline-offset: 2px;
}

button.back-to-list-btn:focus {
  outline: none;
  box-shadow: none;
}
.form-label-with-checkbox {
  display: flex;
  align-items: center;
  gap: 12px; /* Adjust gap here */
  font-weight: 600;
}
