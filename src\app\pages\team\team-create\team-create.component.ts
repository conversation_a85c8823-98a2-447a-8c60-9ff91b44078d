import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TeamService } from '../../../services/team.service';

@Component({
  selector: 'app-team-create',
  standalone: true, 
  imports: [MatIconModule, CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './team-create.component.html',
  styleUrl: './team-create.component.css'
})
export class TeamCreateComponent implements OnInit {
  teamForm!: FormGroup;
  isEditMode = false;
  teamId!: number;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private teamService: TeamService
  ) {}

  ngOnInit(): void {
    this.teamForm = this.fb.group({
      team_name: ['', Validators.required],
      description: ['', Validators.required]
    });

    // Check for 'id' in route to toggle edit mode
    this.route.paramMap.subscribe(params => {
      const idParam = params.get('id');
const parsedId = Number(idParam);

if (idParam && !isNaN(parsedId)) {
  this.isEditMode = true;
  this.teamId = parsedId;
  this.loadTeamData(this.teamId);
} else {
  this.isEditMode = false;
}

    });
  }

  loadTeamData(id: number): void {
    this.teamService.getTeamById(id).subscribe({
      next: (team) => {
        this.teamForm.patchValue({
          team_name: team.teamName,
          description: team.description
        });
      },
      error: (err) => console.error('Failed to load team', err)
    });
  }

  // onSubmit(): void {
  //   if (this.teamForm.invalid) return;

  //   const teamData = {
  //     teamName: this.teamForm.value.team_name,
  //     description: this.teamForm.value.description
  //   };

  //   if (this.isEditMode) {
  //     this.teamService.updateTeam(this.teamId, teamData).subscribe({
  //       next: () => this.router.navigate(['/settings/project/team']),
  //       error: (err) => console.error('Failed to update team', err)
  //     });
  //   } else {
  //     this.teamService.createTeam(teamData).subscribe({
  //       next: () => this.router.navigate(['/settings/project/team']),
  //       error: (err) => console.error('Failed to create team', err)
  //     });
  //   }
  // }

  onSubmit(): void {
    if (this.teamForm.invalid) return;
  
    const teamData = {
      teamId: this.isEditMode ? this.teamId : 0, // Send 0 or omit for create, include for update
      teamName: this.teamForm.value.team_name,
      description: this.teamForm.value.description
    };
  
    if (this.isEditMode) {
      this.teamService.updateTeam(this.teamId, teamData).subscribe({
        next: () => this.router.navigate(['/settings/project/team']),
        error: (err) => console.error('Failed to update team', err)
      });
    } else {
      this.teamService.createTeam(teamData).subscribe({
        next: () => this.router.navigate(['/settings/project/team']),
        error: (err) => console.error('Failed to create team', err)
      });
    }
  }
  

  goBack(): void {
    this.router.navigate(['/settings/project/team']);
  }
}
