import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidatorFn, ValidationErrors } from '@angular/forms';
import { SprintService } from '../../../services/sprint.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Sprint, SprintStatus } from '../../../Models/sprint.model';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-create-sprint',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    MatIconModule
  ],
  templateUrl: './create-sprint.component.html',
  styleUrl: './create-sprint.component.css'
})
export class CreateSprintComponent implements OnInit {
  sprintForm: FormGroup;
  isEditMode = false;
  sprintId: number | null = null;
  statusOptions: SprintStatus[] = [];
  isStatusesLoaded = false;

  constructor(
    private fb: FormBuilder,
    private sprintService: SprintService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.sprintForm = this.fb.group({
      sprint_name: ['', Validators.required],
      start_date: ['', Validators.required],
      end_date: ['', [Validators.required, this.endDateValidator()]],  // ✅ Directly calling the function
      sprint_goal: ['', Validators.required],
      status_name: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadStatuses().then(() => {
      this.route.params.subscribe((params) => {
        if (params['id']) {
          this.isEditMode = true;
          this.sprintId = +params['id'];
          this.loadSprint(this.sprintId);
        }
      });
    });

    // ✅ Watch for changes in start_date and validate end_date dynamically
    this.sprintForm.get('start_date')?.valueChanges.subscribe(() => {
      this.sprintForm.get('end_date')?.updateValueAndValidity();
    });
  }

  private async loadStatuses(): Promise<void> {
    return new Promise((resolve) => {
      this.sprintService.getStatuses().subscribe((statuses) => {
        this.statusOptions = statuses;
        this.isStatusesLoaded = true;
        resolve();
      });
    });
  }

  private loadSprint(id: number): void {
    this.sprintService.getSprintById(id).subscribe((sprint) => {
      console.log("🚀 Sprint Data:", sprint);

      if (!this.isStatusesLoaded) {
        setTimeout(() => this.loadSprint(id), 100);
        return;
      }

      const matchingStatus = this.statusOptions.find(
        (status) => status.status_name === sprint.status_name
      );

      console.log("🎯 Matching Status:", matchingStatus);

      this.sprintForm.patchValue({
        sprint_name: sprint.sprint_name,
        start_date: this.formatDate(sprint.start_date),
        end_date: this.formatDate(sprint.end_date),
        sprint_goal: sprint.sprint_goal,
        status_name: matchingStatus ? matchingStatus.status_name : '' 
      });
    });
  }

  onSubmit(): void {
    if (this.sprintForm.invalid) return;

    const selectedStatus = this.statusOptions.find(
      (status) => status.status_name === this.sprintForm.value.status_name
    );

    if (!selectedStatus) {
      alert('Invalid status selected');
      return;
    }

    const sprintData: Sprint = {
      ...this.sprintForm.value,
      status_name: selectedStatus.status_name
    };

    if (this.isEditMode && this.sprintId !== null) {
      this.sprintService.updateSprint(this.sprintId, sprintData).subscribe(() => {
        this.router.navigate(['/sprint']);
      });
    } else {
      this.sprintService.createSprint(sprintData).subscribe(() => {
        this.router.navigate(['/sprint']);
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/sprint']);
  }

  // private formatDate(date: string): string {
  //   if (!date) return '';
  //   const parsedDate = new Date(date);
  //   return parsedDate.toISOString().split('T')[0];
  // }


  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
      .toISOString()
      .split('T')[0];
  }



  // ✅ Custom Validator Function (INSIDE the component)
  endDateValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const startDate = control.root.get('start_date')?.value;
      const endDate = control.value;

      if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
        return { invalidEndDate: true }; // ❌ Invalid End Date
      }
      return null; // ✅ Valid Date
    };
  }
}
