/* Container Styling */
.scroll-container {
  padding: 20px;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-weight: 600;
  margin-bottom: 4px;
}

.form-input {
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
}

.form-input:focus {
  border-color: #4286f3;
  outline: none;
}
.form-group {
  margin-bottom: 1.5rem;
}

.form-checkbox {
  accent-color: #4286f3; 
}

/* Error message */
.error-message {
  color: #dc2626;
  font-size: 13px;
  margin-top: 4px;
}

/* Button container */
.button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.right-button {
  background-color: #3b82f6;
  color: white;
  padding: 10px 20px;
  border: none;
  font-weight: 600;
  border-radius: 9999px; /* Pill shape */
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 6px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}


.right-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

 .back-button {
  background-color: transparent;
  color: #2c59e0;
  padding: 6px 16px;
  border: none;
  border-radius: 9999px; /* Soft square, not round */
  font-weight: 600;
  font-size: 15px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.back-button:hover,
.back-button:focus {
  background-color: rgba(166, 169, 177, 0.3); /* Light blue on hover/click */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.arrow {
  font-weight: bold;
  font-size: 16px;
  line-height: 1;
}