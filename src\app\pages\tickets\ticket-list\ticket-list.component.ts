import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Router } from "@angular/router";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { MatChipsModule } from "@angular/material/chips";
import { MatMenuModule } from "@angular/material/menu";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatPaginatorModule, PageEvent } from "@angular/material/paginator";
import { MatDialog, MatDialogModule } from "@angular/material/dialog";
import { HttpErrorResponse } from "@angular/common/http";
import {
  Ticket,
  TicketService,
  TicketType,
} from "../../../services/ticket.service";
import { DeleteConfirmationDialogComponent } from "../../../components/delete-confirmation-dialog.component";
import { format } from "date-fns";

type FilterType = "status" | "priority" | "sprint";
type ViewMode = "grid" | "list";

interface Filters {
  search: string;
  status: string[];
  priority: string[];
  sprint: string[];
}

@Component({
  selector: "app-ticket-list",
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatMenuModule,
    MatTooltipModule,
    MatPaginatorModule,
    MatDialogModule,
  ],
  template: `
    <div class="flex flex-col h-[calc(100vh-64px)] overflow-hidden">
      <!-- Header Section -->
      <div class="flex-none p-6 bg-white border-b">
        <div class="max-w-[1600px] mx-auto">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">
                Projects Grid
              </h1>
              <div class="flex mt-4 space-x-4">
                <button
                  *ngFor="let tab of tabs"
                  (click)="setActiveTab(tab.id)"
                  [class]="getTabClass(tab.id)"
                >
                  {{ tab.label }}
                </button>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <button
                mat-raised-button
                (click)="navigateToCreate()"
                class="!bg-blue-600 !text-white hover:!bg-blue-700 !px-4 !py-2 !rounded-lg !font-medium"
              >
                <mat-icon class="!mr-2">add</mat-icon>
                Add New
              </button>
              <div class="flex items-center space-x-2">
                <div class="relative">
                  <input
                    [(ngModel)]="filters.search"
                    (ngModelChange)="applyFilters()"
                    (focus)="searchFocused = true"
                    (blur)="searchFocused = false"
                    placeholder="Search..."
                    class="w-64 h-10 pl-10 pr-4 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none text-sm"
                    [class.ring-1]="searchFocused"
                    [class.ring-blue-500]="searchFocused"
                  />
                  <mat-icon class="absolute left-3 top-2.5 text-gray-400"
                    >search</mat-icon
                  >
                </div>
                <button
                  mat-icon-button
                  (click)="setViewMode('list')"
                  [class.!text-blue-600]="viewMode === 'list'"
                  [class.!text-gray-500]="viewMode !== 'list'"
                  class="!w-10 !h-10"
                >
                  <mat-icon>view_list</mat-icon>
                </button>
                <button
                  mat-icon-button
                  (click)="setViewMode('grid')"
                  [class.!text-blue-600]="viewMode === 'grid'"
                  [class.!text-gray-500]="viewMode !== 'grid'"
                  class="!w-10 !h-10"
                >
                  <mat-icon>grid_view</mat-icon>
                </button>
                <div class="relative">
                  <button
                    mat-icon-button
                    [matMenuTriggerFor]="viewMenu"
                    class="!text-gray-500 !w-10 !h-10"
                  >
                    <mat-icon>more_vert</mat-icon>
                  </button>
                  <mat-menu
                    #viewMenu="matMenu"
                    [xPosition]="'before'"
                    [yPosition]="'below'"
                    [overlapTrigger]="false"
                  >
                    <button mat-menu-item (click)="sortBy('title')">
                      <mat-icon>sort_by_alpha</mat-icon>
                      <span>Sort by Title</span>
                    </button>
                    <button mat-menu-item (click)="sortBy('priorityname')">
                      <mat-icon>priority_high</mat-icon>
                      <span>Sort by Priority</span>
                    </button>
                    <button mat-menu-item (click)="sortBy('last_updated')">
                      <mat-icon>update</mat-icon>
                      <span>Sort by Last Updated</span>
                    </button>
                  </mat-menu>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Content Section with Scroll -->
      <div class="flex-1 overflow-y-auto p-6 bg-gray-50">
        <div class="max-w-[1600px] mx-auto">
          <ng-container [ngSwitch]="viewMode">
            <!-- Grid View -->
            <div
              *ngSwitchCase="'grid'"
              class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              <div
                *ngFor="let ticket of paginatedTickets"
                class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-200"
              >
                <!-- Card Header -->
                <div class="flex items-start justify-between mb-4">
                  <div>
                    <span
                      [class]="getTypeClass(ticket.tickettypename)"
                      class="text-xs font-medium px-2.5 py-1 rounded-full"
                    >
                      {{ ticket.tickettypename }}
                    </span>
                  </div>
                  <div class="relative">
                    <button
                      mat-icon-button
                      [matMenuTriggerFor]="menu"
                      class="!text-gray-400 hover:!text-gray-600"
                    >
                      <mat-icon>more_vert</mat-icon>
                    </button>
                    <mat-menu
                      #menu="matMenu"
                      class="menu-panel"
                      [xPosition]="'before'"
                      [yPosition]="'below'"
                      [overlapTrigger]="false"
                    >
                      <div class="p-1">
                        <button
                          mat-menu-item
                          class="menu-item"
                          (click)="viewTicket(ticket)"
                        >
                          <mat-icon>visibility</mat-icon>
                          <span>View</span>
                        </button>
                        <button
                          mat-menu-item
                          class="menu-item"
                          (click)="editTicket(ticket)"
                        >
                          <mat-icon>edit</mat-icon>
                          <span>Edit</span>
                        </button>
                        <button
                          mat-menu-item
                          class="menu-item danger"
                          (click)="deleteTicket(ticket)"
                        >
                          <mat-icon>delete</mat-icon>
                          <span>Remove</span>
                        </button>
                      </div>
                    </mat-menu>
                  </div>
                </div>

                <!-- Title and Description -->
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  {{ ticket.title }}
                </h3>
                <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                  {{ ticket.description }}
                </p>

                <!-- Team -->
                <div class="mt-6">
                  <div class="text-sm text-gray-500 mb-2">Team:</div>
                  <div class="flex items-center -space-x-2">
                    <img
                      *ngFor="let member of getTeamMembers(ticket)"
                      [src]="
                        'https://placehold.co/32x32/6366F1/ffffff?text=' +
                        member.charAt(0)
                      "
                      [alt]="member"
                      class="w-8 h-8 rounded-full border-2 border-white"
                      [matTooltip]="member"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- List View -->
            <div
              *ngSwitchCase="'list'"
              class="bg-white rounded-lg shadow overflow-hidden"
            >
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Type
                      </th>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Title
                      </th>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Priority
                      </th>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Assigned To
                      </th>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Last Updated
                      </th>
                      <th
                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr *ngFor="let ticket of paginatedTickets">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span
                          [class]="getTypeClass(ticket.tickettypename)"
                          class="text-xs font-medium px-2.5 py-1 rounded-full"
                        >
                          {{ ticket.tickettypename }}
                        </span>
                      </td>
                      <td class="px-6 py-4">
                        <div class="text-sm font-medium text-gray-900">
                          {{ ticket.title }}
                        </div>
                        <div class="text-sm text-gray-500 truncate max-w-md">
                          {{ ticket.description }}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span
                          [class]="getStatusClass(ticket.statusname)"
                          class="text-xs font-medium px-2.5 py-1 rounded-full"
                        >
                          {{ ticket.statusname }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span
                          [class]="getPriorityClass(ticket.priorityname)"
                          class="text-xs font-medium px-2.5 py-1 rounded-full"
                        >
                          {{ ticket.priorityname }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <img
                            [src]="
                              'https://placehold.co/32x32/6366F1/ffffff?text=' +
                              ticket.firstname.charAt(0)
                            "
                            class="w-8 h-8 rounded-full mr-2"
                            [alt]="ticket.firstname"
                          />
                          <div class="text-sm font-medium text-gray-900">
                            {{ ticket.firstname }}
                          </div>
                        </div>
                      </td>
                      <td
                        class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                      >
                        {{ formatDate(ticket.last_updated) }}
                      </td>
                      <td
                        class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                      >
                        <div class="relative inline-block">
                          <button
                            mat-icon-button
                            [matMenuTriggerFor]="actionMenu"
                            class="!text-gray-400"
                          >
                            <mat-icon>more_vert</mat-icon>
                          </button>
                          <mat-menu
                            #actionMenu="matMenu"
                            [xPosition]="'before'"
                            [yPosition]="'below'"
                            [overlapTrigger]="false"
                            class="menu-panel"
                          >
                            <div class="py-1 px-1">
                              <button
                                mat-menu-item
                                (click)="viewTicket(ticket)"
                                class="menu-item"
                              >
                                <mat-icon>visibility</mat-icon>
                                <span>View</span>
                              </button>
                              <button
                                mat-menu-item
                                (click)="editTicket(ticket)"
                                class="menu-item"
                              >
                                <mat-icon>edit</mat-icon>
                                <span>Edit</span>
                              </button>
                              <button
                                mat-menu-item
                                (click)="deleteTicket(ticket)"
                                class="menu-item text-red-600"
                              >
                                <mat-icon>delete</mat-icon>
                                <span>Remove</span>
                              </button>
                            </div>
                          </mat-menu>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </ng-container>

          <!-- Enhanced Pagination -->
          <div class="mt-6">
            <div class="bg-white rounded-xl shadow-sm p-4">
              <div class="flex items-center justify-between">
                <!-- Items per page selector -->
                <div class="flex items-center space-x-3">
                  <span class="text-sm text-gray-500">Show</span>
                  <select
                    [(ngModel)]="pageSize"
                    (ngModelChange)="onPageSizeChange($event)"
                    class="form-select min-w-[80px] !py-1 !text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  >
                    <option *ngFor="let size of pageSizeOptions" [value]="size">
                      {{ size }} items
                    </option>
                  </select>
                </div>

                <!-- Page navigation -->
                <div class="flex items-center space-x-2">
                  <!-- Page info -->
                  <div
                    class="hidden sm:flex items-center mr-4 text-sm text-gray-600"
                  >
                    <span
                      >{{ startIndex + 1 }}-{{ endIndex }} of
                      {{ filteredTickets.length }}</span
                    >
                  </div>

                  <!-- Navigation buttons -->
                  <div class="flex items-center space-x-1">
                    <button
                      (click)="firstPage()"
                      [disabled]="currentPage === 0"
                      class="pagination-button"
                      [class.disabled]="currentPage === 0"
                      matTooltip="First page"
                    >
                      <mat-icon>first_page</mat-icon>
                    </button>

                    <button
                      (click)="previousPage()"
                      [disabled]="currentPage === 0"
                      class="pagination-button"
                      [class.disabled]="currentPage === 0"
                      matTooltip="Previous page"
                    >
                      <mat-icon>chevron_left</mat-icon>
                    </button>

                    <!-- Page numbers -->
                    <div class="hidden sm:flex items-center space-x-1">
                      <ng-container *ngFor="let page of visiblePages">
                        <button
                          *ngIf="page !== '...'"
                          (click)="goToPage(+page - 1)"
                          class="pagination-number"
                          [class.active]="currentPage === +page - 1"
                        >
                          {{ page }}
                        </button>
                        <span *ngIf="page === '...'" class="px-2 text-gray-400"
                          >...</span
                        >
                      </ng-container>
                    </div>

                    <button
                      (click)="nextPage()"
                      [disabled]="currentPage >= totalPages - 1"
                      class="pagination-button"
                      [class.disabled]="currentPage >= totalPages - 1"
                      matTooltip="Next page"
                    >
                      <mat-icon>chevron_right</mat-icon>
                    </button>

                    <button
                      (click)="lastPage()"
                      [disabled]="currentPage >= totalPages - 1"
                      class="pagination-button"
                      [class.disabled]="currentPage >= totalPages - 1"
                      matTooltip="Last page"
                    >
                      <mat-icon>last_page</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      :host ::ng-deep {
        .menu-panel.mat-mdc-menu-panel {
          min-width: 140px !important;
          max-width: 280px !important;
          border-radius: 8px !important;
          margin-top: 4px !important;
          overflow: hidden !important;
        }

        .menu-item.mat-mdc-menu-item {
          min-height: 40px !important;
          line-height: 40px !important;
          font-size: 14px !important;
          border-radius: 4px !important;
          margin: 2px !important;

          .mat-icon {
            margin-right: 12px !important;
            font-size: 20px !important;
            width: 20px !important;
            height: 20px !important;
            color: currentColor !important;
          }

          &:hover {
            background-color: #f3f4f6 !important;
          }
        }

        ::-webkit-scrollbar {
          display: none; /* Hides the scrollbar */
        }

        
      }

      .pagination-button {
  @apply flex items-center justify-center w-9 h-9 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200;
  min-width: 2.25rem; /* Ensures button doesn't shrink */
}

.pagination-number {
  @apply flex items-center justify-center min-w-[36px] h-9 px-3 rounded-lg text-sm font-medium text-gray-600 hover:bg-gray-100 transition-colors duration-200;
}

.pagination-button.disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-transparent hover:text-gray-500;
}

      .pagination-number.active {
        @apply bg-blue-50 text-blue-600 hover:bg-blue-100;
      }
      .form-select {
  @apply block w-full border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-gray-700 text-base px-4 py-2 transition ease-in-out duration-200;
  min-width: 120px; /* Increase dropdown width */
  height: 36px; /* Increase height */
  font-size: 1rem; /* Make text larger */
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 1rem center; /* Adjust arrow position */
  background-repeat: no-repeat;
  background-size: 1.5em; /* Increase dropdown arrow size */
}

    `,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class TicketListComponent implements OnInit {
 
  tickets: Ticket[] = [];
  filteredTickets: Ticket[] = [];
  searchFocused = false;
  viewMode: ViewMode = "grid";
  activeTab = "all";
  sortField: keyof Ticket = "last_updated";
  sortDirection: "asc" | "desc" = "desc";
  pageSize = 6;
  pageSizeOptions = [6, 12, 24, 48];
  currentPage = 0;

  tabs = [
    { id: "all", label: "All" },
    { id: "active", label: "Active" },
    { id: "completed", label: "Completed" },
  ];

  filters: Filters = {
    search: "",
    status: [],
    priority: [],
    sprint: [],
  };
  TicketType: any;
  type: any;

  get startIndex(): number {
    return this.currentPage * this.pageSize;
  }

  get endIndex(): number {
    return Math.min(
      (this.currentPage + 1) * this.pageSize,
      this.filteredTickets.length
    );
  }

  get totalPages(): number {
    return Math.ceil(this.filteredTickets.length / this.pageSize);
  }

  get visiblePages(): (string | number)[] {
    const totalPages = this.totalPages;
    const current = this.currentPage + 1;
    const pages: (string | number)[] = [];

    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    if (current <= 3) {
      return [1, 2, 3, 4, "...", totalPages];
    }

    if (current >= totalPages - 2) {
      return [
        1,
        "...",
        totalPages - 3,
        totalPages - 2,
        totalPages - 1,
        totalPages,
      ];
    }

    return [1, "...", current - 1, current, current + 1, "...", totalPages];
  }

  get paginatedTickets(): Ticket[] {
    const startIndex = this.currentPage * this.pageSize;
    return this.filteredTickets.slice(startIndex, startIndex + this.pageSize);
  }

  constructor(
    private ticketService: TicketService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
  
    this.ticketService.getTicketsObservable().subscribe((tickets) => {
      this.tickets = tickets;
      this.filteredTickets = tickets;
      this.applyFilters();
    });
  }

  setActiveTab(tabId: string) {
    this.activeTab = tabId;
    this.applyFilters();
  }

  setViewMode(mode: ViewMode) {
    this.viewMode = mode;
  }

  navigateToCreate() {
    this.router.navigate(["/tickets/create"]);
  }

  viewTicket(ticket: Ticket) {
    this.router.navigate(["/tickets/details"], {
      queryParams: { id: ticket.ticket_id },
    });
  }

  editTicket(ticket: Ticket) {
    this.router.navigate(["/tickets/create"], {
      queryParams: { id: ticket.ticket_id },
    });
  }

  deleteTicket(ticket: Ticket): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: "400px",
      data: { ticket: { id: ticket.ticket_id, title: ticket.title } },
      panelClass: "custom-dialog-container",
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.ticketService.deleteTicket(ticket.ticket_id).subscribe({
          next: () => {
            // The ticket list will be automatically updated through the BehaviorSubject
            console.log("Ticket deleted successfully");
          },
          error: (error: HttpErrorResponse) => {
            console.error("Error deleting ticket:", error);
          },
        });
      }
    });
  }

  sortBy(field: keyof Ticket) {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === "asc" ? "desc" : "asc";
    } else {
      this.sortField = field;
      this.sortDirection = "asc";
    }
    this.applyFilters();
  }

  applyFilters() {
    console.log("Search value:", this.filters.search);

    let filtered = [...this.tickets];
    //console.log("Before filters:", filtered);

    // Search filter
    if (this.filters.search && this.filters.search.trim() !== "") {
      const search = this.filters.search.trim().toLowerCase();
      filtered = filtered.filter(
        (ticket) =>
          (ticket.title?.toLowerCase() || "").includes(search) ||
          (ticket.description?.toLowerCase() || "").includes(search) ||
          ticket.statusname?.toLowerCase().includes(search) ||
          ticket.priorityname?.toLowerCase().includes(search) ||
          ticket.tickettypename?.toLowerCase().includes(search) ||
          ticket.sprint_name?.toLowerCase().includes(search)
      );
    }

    console.log("After search filter:", filtered);

    // Tab filter
    if (this.activeTab === "active") {
      filtered = filtered.filter((ticket) => ticket?.statusname !== "Closed");
    } else if (this.activeTab === "completed") {
      filtered = filtered.filter((ticket) => ticket?.statusname === "Closed");
    }

    console.log("After tab filter:", filtered);

    // Sorting
    if (this.sortField) {
      filtered.sort((a, b) => {
        const aValue = a[this.sortField] || "";
        const bValue = b[this.sortField] || "";
        const modifier = this.sortDirection === "asc" ? 1 : -1;

        if (typeof aValue === "string" && typeof bValue === "string") {
          return aValue.localeCompare(bValue) * modifier;
        }
        return 0;
      });
    }

    this.filteredTickets = filtered;
    console.log("Final filtered tickets:", this.filteredTickets);
  }
  firstPage(): void {
    this.currentPage = 0;
  }

  lastPage(): void {
    this.currentPage = this.totalPages - 1;
  }

  previousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
    }
  }

  goToPage(page: number): void {
    if (page >= 0 && page < this.totalPages) {
      this.currentPage = page;
    }
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 0; // Reset to first page when changing page size
  }

  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
  }

  getTabClass(tabId: string): string {
    return tabId === this.activeTab
      ? "text-blue-600 border-b-2 border-blue-600 pb-2"
      : "text-gray-500 hover:text-gray-700 pb-2";
  }

  getTypeClass(type: string): string {
    switch (type?.toLowerCase()) {
      case "design":
        return "bg-blue-100 text-blue-800";
      case "development":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case "Open":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      case "Low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  getTeamMembers(ticket: Ticket): string[] {
    return [ticket.firstname];
  }

  formatDate(date: string): string {
    return format(new Date(date), "MMM d, yyyy");
  }
}
