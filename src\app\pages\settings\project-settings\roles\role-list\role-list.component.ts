import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { Role } from '../../../../../Models/rolemodel';
import { trigger, transition, style, animate } from '@angular/animations';
import { RoleService } from '../../../../../services/role.service';
@Component({
  selector: 'app-role-list',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatInputModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    MatSelectModule,
    FormsModule,
    MatDialogModule
  ],
  templateUrl: './role-list.component.html',
  styleUrl: './role-list.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ])
    ])
  ]
})
export class RoleListComponent implements OnInit {
  roles: Role[] = [];
  paginatedRoles: Role[] = [];
  pageSizeOptions: number[] = [5, 7, 10, 25];
  pageSize: number = 5;
  currentPage: number = 0;
  totalRecords: number = 0;
  Math = Math;

  successMessage: string | null = null;
  errorMessage: string | null = null;

  selectedRoleId!: number;
  selectedRoleName!: string;

  @ViewChild('deleteDialog') deleteDialog!: TemplateRef<any>;
  @ViewChild('fkConstraintDialog') fkConstraintDialog!: TemplateRef<any>;

  constructor(
    private roleService: RoleService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadRoles();
  }

  loadRoles(): void {
    this.roleService.getRoles().subscribe(data => {
      this.roles = data;
      this.totalRecords = data.length;

      const maxPage = Math.ceil(this.totalRecords / this.pageSize);
      if (this.currentPage >= maxPage && this.currentPage > 0) {
        this.currentPage = maxPage - 1;
      }

      this.updatePaginatedRoles();
    });
  }

  updatePaginatedRoles(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.totalRecords);
    this.paginatedRoles = this.roles.slice(startIndex, endIndex);
  }

  handlePageEvent(event: any): void {
    this.pageSize = +event.target.value;
    this.currentPage = 0;
    this.updatePaginatedRoles();
  }

  onPageChange(index: number): void {
    this.currentPage = index;
    this.updatePaginatedRoles();
  }

  goToFirstPage(): void {
    this.currentPage = 0;
    this.updatePaginatedRoles();
  }

  goToLastPage(): void {
    this.currentPage = Math.ceil(this.totalRecords / this.pageSize) - 1;
    this.updatePaginatedRoles();
  }

  goToPreviousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updatePaginatedRoles();
    }
  }

  goToNextPage(): void {
    if (this.currentPage < Math.ceil(this.totalRecords / this.pageSize) - 1) {
      this.currentPage++;
      this.updatePaginatedRoles();
    }
  }

  editRole(id: number): void {
    this.router.navigate(['/settings/project/roles/role-edit', id]);
  }

  CreateRole(): void {
    this.router.navigate(['/settings/project/roles/role-create']);
  }

  openDeleteDialog(id: number, name: string): void {
    this.selectedRoleId = id;
    this.selectedRoleName = name;
    this.errorMessage = '';
    this.successMessage = '';

    this.roleService.checkFkConstraint(id).subscribe({
      next: (hasFk: boolean) => {
        if (hasFk) {
          this.dialog.open(this.fkConstraintDialog, { width: '400px', disableClose: true });
        } else {
          this.dialog.open(this.deleteDialog, { width: '400px', disableClose: true });
        }
      },
      error: () => {
        this.errorMessage = 'Error checking foreign key constraint.';
      }
    });
  }

  confirmDelete(): void {
    this.roleService.deleteRole(this.selectedRoleId).subscribe({
      next: () => {
        this.successMessage = 'Role deleted successfully!';
        this.dialog.closeAll();
        this.loadRoles();
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate(['/settings/project/roles/role-list']);
        });
      },
      error: (err) => {
        this.dialog.closeAll();
        this.errorMessage = err.error?.message || 'An unexpected error occurred.';
      }
    });
  }

  cancelDelete(): void {
    this.dialog.closeAll();
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/settings/project/roles/role-list']);
    });
  }
}
