
<nav class="navbar">
    <ul class="menu">
      <!-- Loop through main menus -->
      <li *ngFor="let mainMenu of mainMenus" class="menu-item">
        <a [routerLink]="mainMenu.link" (click)="toggleDropdown(mainMenu.menuId)">
          <i class="material-icons">{{ mainMenu.icon }}</i>
          {{ mainMenu.label }}
        </a>
  
        <!-- Dropdown button if menu has children -->
        <button *ngIf="mainMenu.children?.length" (click)="toggleDropdown(mainMenu.menuId)">
          ▼
        </button>
  
        <!-- Submenu -->
        <ul class="submenu" *ngIf="mainMenu.children?.length && activeDropdown === mainMenu.menuId">
          <li *ngFor="let subMenu of mainMenu.children" class="submenu-item">
            <a [routerLink]="subMenu.link">{{ subMenu.label }}</a>
  
            <!-- Nested submenus -->
            <ul class="child-submenu" *ngIf="subMenu.children?.length">
              <li *ngFor="let childMenu of subMenu.children" class="child-menu-item">
                <a [routerLink]="childMenu.link">{{ childMenu.label }}</a>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
  