import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Tag } from '../Models/tag.model';
import { Observable } from 'rxjs';
// export interface Tag {
//   tag_id: number;
//   tag_name: string;
//    isactive: boolean;
// }

@Injectable({
  providedIn: 'root'
})
export class TagService {
  private baseUrl = 'http://localhost:3000/api/Tags'; // adjust if needed

  constructor(private http: HttpClient) {}

  getAllTags() {
    return this.http.get<Tag[]>(`${this.baseUrl}/AllTags`);
  }

  getTagById(id: number) {
    return this.http.get<Tag>(`${this.baseUrl}/${id}`);
  }

  createTag(tag: Partial<Tag>) {
    return this.http.post(`${this.baseUrl}`, tag);
  }

  // updateTag(id: number, tag: Tag) {
  //   return this.http.put(`${this.baseUrl}/${id}`, tag);
  // }
  updateTag(id: number, tag: Tag) {
    return this.http.put(`${this.baseUrl}/${id}`, tag, { responseType: 'text' });
  }
  

  deleteTag(id: number) {
    return this.http.delete(`${this.baseUrl}/${id}`,{ responseType: 'text' as 'json' });
  }

checkFkConstraint(id: number): Observable<boolean> {
  return this.http.get<boolean>(`${this.baseUrl}/check-fk/${id}`);
}

}
