:host {
    display: block;
    height: 100vh;
    overflow-y: auto;
    padding: 2rem;
    background-color: #F9FAFB;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .form-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #E5E7EB;
    border-radius: 0.25rem;
  }
  
  .error-message {
    color: red;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  
  /* .submit-container {
    display: flex;
    justify-content: flex-end; /* Align button to the right */
    /* margin-top: 1.5rem;
  } */
   

  .submit-container {
    display: flex;
    justify-content: flex-end; /* Aligns button to the right */
    padding-top: 1rem; /* Adds spacing above the button */
  }
  


  /* button[type="submit"] {
    background-color: #007bff !important; /* Blue color */
    /* color: white !important;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    transition: background 0.3s ease-in-out;
  } */
  
  /* button[type="submit"]:hover {
    background-color: #0056b3 !important; /* Darker blue on hover */
  /* } */
   

  .back-button {
    display: flex;
    align-items: center;
    gap: 4px; /* Space between icon and text */
  }
  
  .back-button mat-icon {
    font-size: 20px; /* Adjust icon size */
  }
  

  .w-1\/2 {
    width: 50%;
  }
  
  /* .back-button {
    background-color: transparent; /* No background 
    color: #1c3faa; /* Blue text 
    padding: 8px 16px;
    border: 2px solid transparent; /* No border initially 
    border-radius: 30px; /* Rounded corners *
    transition: border-color 0.3s ease, color 0.3s ease;
  } */
  
  /* .back-button:hover {
    background-color: 8ca7fa;
    border-color: #8ca7fa; /* Blue border on hover 
    color: #1c3faa;  Keep text blue 
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);  Adds a shadow effect 
  } */
  
  .back-button:hover {
  background-color: #adc0fc; /* Correct hex color */
  border-color: #8ca7fa;     /* Blue border on hover */
  color: #1c3faa;            /* Text color */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2); /* Shadow effect */
  transition: all 0.3s ease; /* Smooth transition */
}


.back-button {
  background-color: transparent;
  border: 2px solid transparent;
  color: #809ffc;
  padding: 6px 14px;
  border-radius: 30px;
  transition: all 0.3s ease;
}

  

  /* .submit-container button {
    background-color:rgb(223, 222, 220); /* No background */
    /* Blue text */
    /* padding: 8px 16px;
    border: 2px solid transparent; /* No border initially */
    /* border-radius: 30px; Rounded corners 
    transition: border-color 0.3s ease, color 0.3s ease;
  }  */
  
  /* .submit-container button:hover {
    border-color: #8ca7fa; /* Blue border on hover 
    color: #1c3faa; Keep text blue 
    transform: scale(1.05); Slightly enlarges the button 
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);  Adds a shadow effect 
  } */

  /* Base style (gray button) */
.submit-container button {
  background-color: rgb(223, 222, 220); /* Gray background */
  color: #ffffff;
  padding: 8px 16px;
  border: 2px solid transparent;
  border-radius: 30px;
  transition: all 0.3s ease;
}

/* Hover style (when still gray) */
.submit-container button:hover {
  border-color: #8ca7fa;
  color: #1c3faa;
  transform: scale(1.05);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

/* Valid form (blue background) */
.btn-valid {
  background-color: #3b82f6 !important; /* Tailwind blue-500 */
  color: #ffffff !important;
  border: none;
}

.btn-valid:hover {
  background-color: #2563eb !important; /* blue-600 */
  color: #ffffff;
  border: none;
  transform: scale(1.05);
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
}

   