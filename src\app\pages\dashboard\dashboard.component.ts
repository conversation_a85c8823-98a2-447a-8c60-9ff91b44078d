import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { TicketService, Ticket, TicketStatus } from '../../services/ticket.service';
import { format } from 'date-fns';

interface TicketStats {
  total: number;
  open: number;
  inProgress: number;
  resolved: number;
  closed: number;
}

interface PriorityStats {
  low: number;
  medium: number;
  high: number;
  critical: number;
}

@Component({
    selector: 'app-dashboard',
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatTooltipModule
    ],
    template: `
    <div class="h-[calc(100vh-64px)] overflow-y-auto bg-gray-50">
      <div class="p-6 max-w-[1600px] mx-auto">
        <!-- Header -->
        <div class="mb-8">
          <h1 class="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
          <p class="mt-1 text-sm text-gray-500">Track your project's performance and ticket statistics</p>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Tickets -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center mr-4">
                  <mat-icon class="text-blue-600">confirmation_number</mat-icon>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500">Total Tickets</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ticketStats.total}}</p>
                </div>
              </div>
              <button mat-icon-button [matMenuTriggerFor]="menu" class="!text-gray-400">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #menu="matMenu">
                <button mat-menu-item (click)="navigateToTickets()">
                  <mat-icon>list</mat-icon>
                  <span>View All</span>
                </button>
                <button mat-menu-item (click)="navigateToCreateTicket()">
                  <mat-icon>add</mat-icon>
                  <span>Create New</span>
                </button>
              </mat-menu>
            </div>
            <div class="flex items-center text-sm">
              <span class="text-green-600 flex items-center">
                <mat-icon class="text-base mr-1">trending_up</mat-icon>
                12%
              </span>
              <span class="text-gray-500 ml-2">vs last month</span>
            </div>
          </div>

          <!-- Open Tickets -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-lg bg-yellow-100 flex items-center justify-center mr-4">
                  <mat-icon class="text-yellow-600">pending_actions</mat-icon>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500">Open Tickets</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ticketStats.open}}</p>
                </div>
              </div>
              <button mat-icon-button [matMenuTriggerFor]="openMenu" class="!text-gray-400">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #openMenu="matMenu">
                <button mat-menu-item (click)="filterTickets('open')">
                  <mat-icon>filter_list</mat-icon>
                  <span>View Open</span>
                </button>
              </mat-menu>
            </div>
            <div class="flex items-center text-sm">
              <span class="text-red-600 flex items-center">
                <mat-icon class="text-base mr-1">trending_down</mat-icon>
                8%
              </span>
              <span class="text-gray-500 ml-2">vs last month</span>
            </div>
          </div>

          <!-- In Progress -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center mr-4">
                  <mat-icon class="text-indigo-600">hourglass_bottom</mat-icon>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500">In Progress</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ticketStats.inProgress}}</p>
                </div>
              </div>
              <button mat-icon-button [matMenuTriggerFor]="progressMenu" class="!text-gray-400">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #progressMenu="matMenu">
                <button mat-menu-item (click)="filterTickets('inProgress')">
                  <mat-icon>filter_list</mat-icon>
                  <span>View In Progress</span>
                </button>
              </mat-menu>
            </div>
            <div class="flex items-center text-sm">
              <span class="text-green-600 flex items-center">
                <mat-icon class="text-base mr-1">trending_up</mat-icon>
                24%
              </span>
              <span class="text-gray-500 ml-2">vs last month</span>
            </div>
          </div>

          <!-- Completed -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center mr-4">
                  <mat-icon class="text-green-600">task_alt</mat-icon>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500">Completed</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ticketStats.closed}}</p>
                </div>
              </div>
              <button mat-icon-button [matMenuTriggerFor]="completedMenu" class="!text-gray-400">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #completedMenu="matMenu">
                <button mat-menu-item (click)="filterTickets('completed')">
                  <mat-icon>filter_list</mat-icon>
                  <span>View Completed</span>
                </button>
              </mat-menu>
            </div>
            <div class="flex items-center text-sm">
              <span class="text-green-600 flex items-center">
                <mat-icon class="text-base mr-1">trending_up</mat-icon>
                16%
              </span>
              <span class="text-gray-500 ml-2">vs last month</span>
            </div>
          </div>
        </div>

        <!-- Priority Distribution -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <!-- Priority Stats -->
          <div class="bg-white rounded-xl shadow-sm p-6 lg:col-span-2">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Priority Distribution</h3>
            <div class="space-y-4">
              <!-- Critical -->
              <div>
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">Critical</span>
                  <span class="text-sm font-medium text-gray-900">{{priorityStats.critical}}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-red-600 h-2 rounded-full" 
                       [style.width.%]="getPriorityPercentage('critical')"></div>
                </div>
              </div>

              <!-- High -->
              <div>
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">High</span>
                  <span class="text-sm font-medium text-gray-900">{{priorityStats.high}}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-orange-500 h-2 rounded-full"
                       [style.width.%]="getPriorityPercentage('high')"></div>
                </div>
              </div>

              <!-- Medium -->
              <div>
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">Medium</span>
                  <span class="text-sm font-medium text-gray-900">{{priorityStats.medium}}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-yellow-500 h-2 rounded-full"
                       [style.width.%]="getPriorityPercentage('medium')"></div>
                </div>
              </div>

              <!-- Low -->
              <div>
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">Low</span>
                  <span class="text-sm font-medium text-gray-900">{{priorityStats.low}}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full"
                       [style.width.%]="getPriorityPercentage('low')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            <div class="space-y-4">
              <button mat-flat-button 
                      color="primary" 
                      class="w-full !py-3 !rounded-lg"
                      (click)="navigateToCreateTicket()">
                <mat-icon class="!mr-2">add</mat-icon>
                Create New Ticket
              </button>
              <button mat-stroked-button 
                      class="w-full !py-3 !rounded-lg"
                      (click)="navigateToTickets()">
                <mat-icon class="!mr-2">list</mat-icon>
                View All Tickets
              </button>
              <button mat-stroked-button 
                      class="w-full !py-3 !rounded-lg"
                      (click)="navigateToReports()">
                <mat-icon class="!mr-2">analytics</mat-icon>
                View Reports
              </button>
              <button mat-stroked-button 
                      class="w-full !py-3 !rounded-lg"
                      (click)="navigateToSettings()">
                <mat-icon class="!mr-2">settings</mat-icon>
                Settings
              </button>
            </div>
          </div>
        </div>

        <!-- Recent Tickets -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">Recent Tickets</h3>
              <button mat-button color="primary" (click)="navigateToTickets()">
                View All
                <mat-icon class="!ml-2">arrow_forward</mat-icon>
              </button>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr *ngFor="let ticket of recentTickets">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ticket.ticket_id}}</td>
                  <td class="px-6 py-4">
                    <div class="text-sm font-medium text-gray-900">{{ticket.title}}</div>
                    <div class="text-sm text-gray-500 truncate max-w-md">{{ticket.description}}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span [class]="getStatusClass(ticket.statusname)" class="px-2.5 py-1 text-xs font-medium rounded-full">
                      {{ticket.statusname}}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span [class]="getPriorityClass(ticket.priorityname)" class="px-2.5 py-1 text-xs font-medium rounded-full">
                      {{ticket.priorityname}}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <img [src]="'https://placehold.co/32x32/6366F1/ffffff?text=' + ticket.firstname.charAt(0)"
                           class="w-8 h-8 rounded-full mr-2"
                           [alt]="ticket.firstname">
                      <div class="text-sm font-medium text-gray-900">{{ticket.firstname}}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{formatDate(ticket.last_updated)}}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button mat-icon-button 
                            [matMenuTriggerFor]="actionMenu"
                            class="!text-gray-400">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                    <mat-menu #actionMenu="matMenu">
                      <button mat-menu-item (click)="viewTicket(ticket)">
                        <mat-icon>visibility</mat-icon>
                        <span>View</span>
                      </button>
                      <button mat-menu-item (click)="editTicket(ticket)">
                        <mat-icon>edit</mat-icon>
                        <span>Edit</span>
                      </button>
                    </mat-menu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `,
    styles: [`
    :host {
      display: block;
      height: 100%;
    }
  `]
})
export class DashboardComponent implements OnInit {
  ticketStats: TicketStats = {
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    closed: 0
  };

  priorityStats: PriorityStats = {
    low: 0,
    medium: 0,
    high: 0,
    critical: 0
  };

  recentTickets: Ticket[] = [];

  constructor(
    private ticketService: TicketService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.ticketService.getTickets().subscribe(tickets => {
      // Calculate ticket stats
      this.ticketStats = {
        total: tickets.length,
        open: tickets.filter(t => t.statusname === 'New').length,
        inProgress: tickets.filter(t => t.statusname === 'In Progress').length,
        resolved: tickets.filter(t => t.statusname === 'Resolved').length,
        closed: tickets.filter(t => t.statusname === 'Closed').length
      };

      // Calculate priority stats
      this.priorityStats = {
        low: tickets.filter(t => t.priorityname === 'Low').length,
        medium: tickets.filter(t => t.priorityname === 'Medium').length,
        high: tickets.filter(t => t.priorityname === 'High').length,
        critical: tickets.filter(t => t.priorityname === 'Critical').length
      };

      // Get recent tickets
      this.recentTickets = tickets
        .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())
        .slice(0, 5);
    });
  }

  getPriorityPercentage(priority: keyof PriorityStats): number {
    const total = Object.values(this.priorityStats).reduce((sum, count) => sum + count, 0);
    return total > 0 ? (this.priorityStats[priority] / total) * 100 : 0;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'New':
        return 'bg-yellow-100 text-yellow-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Resolved':
        return 'bg-green-100 text-green-800';
      case 'Closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatDate(date: string): string {
    return format(new Date(date), 'MMM d, yyyy');
  }

  navigateToTickets() {
    this.router.navigate(['/tickets/list']);
  }

  navigateToCreateTicket() {
    this.router.navigate(['/tickets/create']);
  }

  navigateToReports() {
    this.router.navigate(['/reports/sprint']);
  }

  navigateToSettings() {
    this.router.navigate(['/settings/project']);
  }

  filterTickets(status: string) {
    //console.log('status: ', status);
    this.router.navigate(['/tickets/list'], { 
      queryParams: { status: status }
    });
  }

  viewTicket(ticket: Ticket) {
    this.router.navigate(['/tickets/details'], { 
      queryParams: { id: ticket.ticket_id }
    });
  }

  editTicket(ticket: Ticket) {
    this.router.navigate(['/tickets/create'], { 
      queryParams: { id: ticket.ticket_id }
    });
  }
}