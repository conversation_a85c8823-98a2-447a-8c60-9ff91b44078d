@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #F9FAFB;
}

.mat-icon {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}

/* Menu Styles */
.mat-mdc-menu-panel {
  min-width: unset !important;
  border-radius: 1rem !important;
  overflow: hidden !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.mat-mdc-menu-content {
  padding: 0 !important;
}

.mat-mdc-menu-item {
  border-radius: 0.5rem !important;
  margin: 0.125rem !important;
  min-height: 2.5rem !important;
  line-height: 2.5rem !important;
  font-size: 0.875rem !important;
}

.mat-mdc-menu-item .mat-icon {
  margin-right: 0.75rem !important;
  color: #6B7280 !important;
}

.mat-mdc-menu-item:hover {
  background-color: #F3F4F6 !important;
}

.mat-mdc-menu-item:hover .mat-icon {
  color: #2563EB !important;
}

/* Profile Menu Styles */
.profile-menu {
  margin-top: 0.5rem !important;
  min-width: 320px !important;
}

.profile-menu .mat-mdc-menu-content {
  padding: 1rem !important;
}

/* Form Styles */
input[type="text"],
input[type="number"],
textarea,
select {
  @apply w-full px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm;
  transition: all 0.2s ease-in-out;
}

input[type="text"]:hover,
input[type="number"]:hover,
textarea:hover,
select:hover {
  @apply border-gray-400;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  @apply outline-none border-blue-500 ring-1 ring-blue-500;
}

input[type="text"]::placeholder,
input[type="number"]::placeholder,
textarea::placeholder {
  @apply text-gray-400;
}

/* Button Styles */
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
}

/* Form Layout */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm;
  transition: all 0.2s ease-in-out;
}

.form-input:hover {
  @apply border-gray-400;
}

.form-input:focus {
  @apply outline-none border-blue-500 ring-1 ring-blue-500;
}

/* Error States */
.form-input.error {
  @apply border-red-300 text-red-900 placeholder-red-300;
}

.form-input.error:focus {
  @apply border-red-500 ring-1 ring-red-500;
}

.error-message {
  @apply mt-2 text-sm text-red-600;
}


::-webkit-scrollbar {
  display: none; /* Hides the scrollbar */
}

/* Select Styles */
select.form-input {
  @apply pr-10 appearance-none bg-no-repeat;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
}

/* Textarea Styles */
textarea.form-input {
  @apply min-h-[100px] resize-y;
}
html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }
