import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SprintService } from '../../services/sprint.service';
import { Router } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-sprint',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    FormsModule,
    MatDialogModule
  ],
  templateUrl: './sprint.component.html',
  styleUrls: ['./sprint.component.css'],
})
export class SprintComponent implements OnInit {
  sprints: any[] = [];
  paginatedSprints: any[] = [];

  Math = Math;
  currentPage: number = 0;
  pageSize: number = 5;
  pageSizeOptions: number[] = [5, 10, 20, 50];

  totalRecords: number = 0;
  totalPages: number = 0;

  isLoading: boolean = true;

  constructor(
    private router: Router,
    private sprintService: SprintService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.loadSprints();
  }

  loadSprints() {
    this.isLoading = true;
    this.sprintService.getSprints().subscribe((data: any[]) => {
      this.sprints = data;
      this.totalRecords = data.length;
      this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
      this.paginateData();
      this.isLoading = false;
    });
  }

  paginateData() {
    const start = this.currentPage * this.pageSize;
    const end = start + this.pageSize;
    this.paginatedSprints = this.sprints.slice(start, end);
  }

  onPageSizeChange() {
    this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
    this.currentPage = 0;
    this.paginateData();
  }

  getPageNumbers(): number[] {
    return Array(this.totalPages).fill(0).map((_, i) => i);
  }

  goToFirstPage() {
    this.currentPage = 0;
    this.paginateData();
  }

  goToPreviousPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.paginateData();
    }
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.paginateData();
  }

  goToNextPage() {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
      this.paginateData();
    }
  }

  goToLastPage() {
    this.currentPage = this.totalPages - 1;
    this.paginateData();
  }

  navigateToCreate() {
    this.router.navigate(['/sprints/create']);
  }

  navigateToEdit(sprint: any) {
    this.router.navigate(['/sprints/edit', sprint.sprint_id]);
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'status-active';
      case 'completed':
        return 'status-completed';
      case 'pending':
        return 'status-pending';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return 'status-default';
    }
  }

async confirmDelete(sprint: any) {
  const { SprintConfirmDialogComponent } = await import('./sprintconfirm-dialog.component');

  // First, check if the sprint can be deleted
  this.sprintService.canDeleteSprint(sprint.sprint_id).subscribe(canDeleteResponse => {
    const canDelete = canDeleteResponse.canDelete;

    const dialogRef = this.dialog.open(SprintConfirmDialogComponent, {
      width: '400px',
  
      data: {
        id: sprint.sprint_id,
        name: sprint.sprint_name,
        error: !canDelete,
        showConfirm: canDelete
      },
      panelClass: 'custom-delete-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && canDelete) {
        // Proceed to delete
        this.sprintService.deleteSprint(sprint.sprint_id).subscribe({
          next: () => this.loadSprints(),
          error: (err) => console.error('Failed to delete sprint:', err)
        });
      }
    });
  });
}



}
