import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { trigger, transition, style, animate } from '@angular/animations';
import { Priority, PriorityService } from '../../../services/priority.service';

@Component({
  selector: 'app-priority-list',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatInputModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    MatSelectModule,
    FormsModule,
    MatDialogModule
  ],
  templateUrl: './priority-list.component.html',
  styleUrl: './priority-list.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ])
    ])
  ]
})
export class PriorityListComponent implements OnInit {
  priorities: Priority[] = [];
  paginatedPriorities: Priority[] = [];
  pageSizeOptions: number[] = [5, 7, 10, 25];
  pageSize: number = 5;
  currentPage: number = 0;
  totalRecords: number = 0;
  Math = Math;

  successMessage: string | null = null;
  errorMessage: string | null = null;

  selectedPriorityId!: number;
  selectedPriorityName!: string;

  @ViewChild('deleteDialog') deleteDialog!: TemplateRef<any>;
  @ViewChild('fkConstraintDialog') fkConstraintDialog!: TemplateRef<any>;

  constructor(
    private priorityService: PriorityService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadPriorities();
  }

  loadPriorities(): void {
    this.priorityService.getPriorities().subscribe(data => {
      this.priorities = data;
      this.totalRecords = data.length;

      const maxPage = Math.ceil(this.totalRecords / this.pageSize);
      if (this.currentPage >= maxPage && this.currentPage > 0) {
        this.currentPage = maxPage - 1;
      }

      this.updatePaginatedPriorities();
    });
  }

  updatePaginatedPriorities(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.totalRecords);
    this.paginatedPriorities = this.priorities.slice(startIndex, endIndex);
  }

  handlePageEvent(event: any): void {
    this.pageSize = +event.target.value;
    this.currentPage = 0;
    this.updatePaginatedPriorities();
  }

  onPageChange(index: number): void {
    this.currentPage = index;
    this.updatePaginatedPriorities();
  }

  goToFirstPage(): void {
    this.currentPage = 0;
    this.updatePaginatedPriorities();
  }

  goToLastPage(): void {
    this.currentPage = Math.ceil(this.totalRecords / this.pageSize) - 1;
    this.updatePaginatedPriorities();
  }

  goToPreviousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updatePaginatedPriorities();
    }
  }

  goToNextPage(): void {
    if (this.currentPage < Math.ceil(this.totalRecords / this.pageSize) - 1) {
      this.currentPage++;
      this.updatePaginatedPriorities();
    }
  }

  editPriority(id: number): void {
    this.router.navigate(['/settings/project/priority/priority-edit', id]);
  }

  createPriority(): void {
    this.router.navigate(['/settings/project/priority/priority-create']);
  }

  openDeleteDialog(id: number, name: string): void {
    this.selectedPriorityId = id;
    this.selectedPriorityName = name;
    this.errorMessage = '';
    this.successMessage = '';

    this.priorityService.checkFkConstraint(id).subscribe({
      next: (hasFk: boolean) => {
        if (hasFk) {
          this.dialog.open(this.fkConstraintDialog, { width: '400px', disableClose: true });
        } else {
          this.dialog.open(this.deleteDialog, { width: '400px', disableClose: true });
        }
      },
      error: () => {
        this.errorMessage = 'Error checking foreign key constraint.';
      }
    });
  }

  confirmDelete(): void {
    this.priorityService.deletePriority(this.selectedPriorityId).subscribe({
      next: () => {
        this.successMessage = 'Priority deleted successfully!';
        this.dialog.closeAll();
        this.loadPriorities();
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate(['/settings/project/priority/priority-list']);
        });
      },
      error: (err) => {
        this.dialog.closeAll();
        this.errorMessage = err.error?.message || 'An unexpected error occurred.';
      }
    });
  }

  cancelDelete(): void {
    this.dialog.closeAll();
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/settings/project/priority/priority-list']);
    });
  }
}
