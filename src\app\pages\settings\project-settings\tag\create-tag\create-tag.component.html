<div class="scroll-container">
  <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ isEdit ? 'Edit Tag' : 'Create Tag' }}
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEdit ? 'Update the tag details below' : 'Fill in the information below to create a new tag' }}
          </p>
        </div>
        <!-- Back Button -->
  <button
  (click)="goBack()"
  class="back-to-list-btn flex items-center gap-1">
  <mat-icon>arrow_back</mat-icon> Back to List
</button>


      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="tagForm" (ngSubmit)="onSubmit()" class="p-6 form-container">
      <div class="form-group">
        <label class="form-label" [ngClass]="{'required': tagForm.get('tag_name')?.hasError('required')}">Tag Name</label>
        <input
          type="text"
          formControlName="tag_name"
          class="form-input"
          placeholder="Enter Tag Name"
        />

        <!-- Validation Error Messages -->
        <div *ngIf="tagForm.get('tag_name')?.touched || tagForm.get('tag_name')?.dirty" class="error-message mt-1 text-sm text-red-600">
          <!-- Required error message -->
          <div *ngIf="tagForm.get('tag_name')?.hasError('required')" class="error-text">
            Tag Name is required
          </div>

          <!-- Duplicate error message -->
          <div *ngIf="tagForm.get('tag_name')?.hasError('duplicate')" class="error-text">
            Tag Name already exists.Please choose a different name.
          </div>
        </div>
      </div>

      <!-- ✅ New: Status Checkbox -->
     <!-- ✅ Fix checkbox to use 'isactive' -->
<div class="form-group">
  <label class="form-label-with-checkbox">
    Status
    <input
      type="checkbox"
      formControlName="isactive"
      class="custom-checkbox"
    />
  </label>
</div>



      <!-- Button Container -->
      <div class="button-container mt-4">
        <button type="submit" [disabled]="tagForm.invalid" class="right-button bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center gap-1">
          <mat-icon *ngIf="isEdit">update</mat-icon>
          <mat-icon *ngIf="!isEdit">add</mat-icon>
          {{ isEdit ? 'Update Tag' : 'Create Tag' }}
        </button>
      </div>
    </form>
  </div>
</div>