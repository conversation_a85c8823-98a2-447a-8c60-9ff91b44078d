import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Router, ActivatedRoute } from "@angular/router";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatChipsModule } from "@angular/material/chips";
import { MatTooltipModule } from "@angular/material/tooltip";
import {
  Ticket,
  TicketService,
  Comment,
} from "../../../services/ticket.service";
import { FormsModule } from "@angular/forms";
import { format } from "date-fns";
import { PickerModule } from "@ctrl/ngx-emoji-mart";

@Component({
  selector: "app-ticket-details",
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule,
    FormsModule,
    PickerModule,
  ],
  styles: [
    `
      :host {
        display: block;
        height: 100vh;
        overflow-y: auto;
        background-color: #f9fafb;
      }

      .scroll-container {
        min-height: 100%;
        padding: 2rem 0;
      }

      .content-container {
        max-width: 64rem;
        margin: 0 auto;
      }

      .details-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      }

      .section {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
      }

      .section:last-child {
        border-bottom: none;
      }

      .metadata {
        background-color: #f9fafb;
        border-top: 1px solid #e5e7eb;
        border-radius: 0 0 0.5rem 0.5rem;
      }
      .file-drop-zone {
        border: 2px dashed #e5e7eb;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
        transition: all 0.2s ease;
        cursor: pointer;
      }

      .file-drop-zone.dragover {
        border-color: #3b82f6;
        background-color: #eff6ff;
      }
    `,
  ],
  template: `
    <div class="scroll-container">
      <div class="content-container px-6">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <div>
            <div class="flex items-center gap-4">
              <h1 class="text-2xl font-semibold text-gray-900">
                Ticket Details
              </h1>
              <span
                [class]="getStatusClass(ticket?.statusname)"
                class="px-3 py-1 rounded-full text-sm font-medium"
              >
                {{ ticket?.statusname }}
              </span>
            </div>
            <p class="mt-1 text-sm text-gray-500">
              View and manage ticket information
            </p>
          </div>
          <div class="flex items-center gap-4">
            <button
              mat-button
              (click)="editTicket()"
              class="!text-blue-600 hover:!bg-blue-50"
            >
              <mat-icon class="!mr-2">edit</mat-icon>
              Edit
            </button>
            <button
              mat-button
              (click)="goBack()"
              class="!text-gray-600 hover:!bg-gray-50"
            >
              <mat-icon class="!mr-2">arrow_back</mat-icon>
              Back to List
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="details-card">
          <!-- Basic Information -->
          <div class="section">
            <h2 class="text-lg font-medium text-gray-900 mb-4">
              Basic Information
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Title -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Title</label
                >
                <p class="mt-1 text-base text-gray-900">{{ ticket?.title }}</p>
              </div>

              <!-- Type -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Type</label
                >
                <span
                  [class]="getTypeClass(ticket?.tickettypename)"
                  class="inline-block mt-1 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {{ ticket?.tickettypename }}
                </span>
              </div>

              <!-- Description -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-500"
                  >Description</label
                >
                <p class="mt-1 text-base text-gray-900 whitespace-pre-line">
                  {{ ticket?.description }}
                </p>
              </div>
            </div>
          </div>

          <!-- Status Information -->
          <div class="section">
            <h2 class="text-lg font-medium text-gray-900 mb-4">
              Status Information
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Status -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Status</label
                >
                <span
                  [class]="getStatusClass(ticket?.statusname)"
                  class="inline-block mt-1 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {{ ticket?.statusname }}
                </span>
              </div>

              <!-- Priority -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Priority</label
                >
                <span
                  [class]="getPriorityClass(ticket?.priorityname)"
                  class="inline-block mt-1 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {{ ticket?.priorityname }}
                </span>
              </div>

              <!-- Sprint -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Sprint</label
                >
                <p class="mt-1 text-base text-gray-900">
                  {{ ticket?.sprint_name }}
                </p>
              </div>
            </div>
          </div>

          <!-- Assignment Information -->
          <div class="section">
            <h2 class="text-lg font-medium text-gray-900 mb-4">
              Assignment Information
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Assignee -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Assignee</label
                >
                <div class="flex items-center mt-1">
                  <img
                    [src]="
                      'https://placehold.co/32x32/6366F1/ffffff?text=' +
                      ticket?.firstname?.charAt(0)
                    "
                    [alt]="ticket?.firstname"
                    class="w-8 h-8 rounded-full mr-2"
                  />
                  <span class="text-base text-gray-900"
                    >{{ ticket?.firstname }}
                  </span>
                </div>
              </div>

              <!-- Estimated Time -->
              <div>
                <label class="block text-sm font-medium text-gray-500"
                  >Estimated Time</label
                >
                <p class="mt-1 text-base text-gray-900">
                  {{ ticket?.estimated_time }} hours
                </p>
              </div>
            </div>
          </div>

          <!-- Tags -->
          <div class="section">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Tags</h2>
            <div class="flex flex-wrap gap-2">
              <span
                *ngFor="let tag of ticket?.tags"
                class="px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700"
              >
                {{ tag.tag_name }}
              </span>
              <span *ngIf="!ticket?.tags?.length" class="text-gray-500 text-sm"
                >No tags added</span
              >
            </div>
          </div>

          <!-- Attachments -->
          <div class="section">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Attachments</h2>
            <div class="space-y-2">
              <div
                *ngFor="let attachment of ticket?.attachments"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div class="flex items-center">
                  <mat-icon class="text-gray-400 mr-2">{{
                    getFileIcon(attachment.file_name)
                  }}</mat-icon>
                  <span class="text-sm text-gray-900">{{
                    attachment.file_name
                  }}</span>
                </div>
                <a
                  [href]="'http://localhost:3000/' + attachment.file_url"
                  target="_blank"
                  mat-icon-button
                  class="!text-gray-400 hover:!text-blue-600"
                  matTooltip="Download"
                >
                  <mat-icon>download</mat-icon>
                </a>
              </div>
              <p
                *ngIf="!ticket?.attachments?.length"
                class="text-gray-500 text-sm"
              >
                No attachments added
              </p>
            </div>
          </div>

          <!-- Comments Section -->

          <div class="section">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Comments</h2>

            <!-- New Comment Input -->
            <div class="flex items-start gap-2 relative">
              <textarea
                [(ngModel)]="newComment"
                class="w-full p-2 border rounded-md"
                placeholder="Add a comment..."
              ></textarea>
            </div>
            <br />
            <div class="form-group">
              <label class="form-label">Attachments</label>
              <div
                class="file-drop-zone"
                [class.dragover]="isDragging"
                (click)="fileInput.click()"
              >
                <input
                  #fileInput
                  type="file"
                  multiple
                  class="hidden"
                  (change)="onFileSelected($event)"
                />
                <mat-icon class="text-gray-400 text-4xl mb-2"
                  >cloud_upload</mat-icon
                >
                <p class="text-sm text-gray-600 mb-1">
                  <span class="text-blue-600 font-medium">Click to upload</span>
                  or drag and drop
                </p>
                <p class="text-xs text-gray-500">
                  PNG, JPG, PDF (max 10MB per file)
                </p>
              </div>
              <!-- File List -->
              <div
                *ngIf="selectedFiles.length > 0"
                class="mt-4 space-y-2 max-h-48 overflow-y-auto"
              >
                <div
                  *ngFor="let file of selectedFiles; let i = index"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center overflow-hidden">
                    <mat-icon class="text-gray-400 mr-2 flex-shrink-0">
                      {{ getFileIcon(file.name) }}
                    </mat-icon>
                    <span class="text-sm text-gray-600 truncate">{{
                      file.name
                    }}</span>
                    <!-- <span class="text-xs text-gray-400 ml-2">
          {{formatFileSize(file.size)}}
        </span> -->
                  </div>
                  <button
                    type="button"
                    mat-icon-button
                    (click)="removeFile(i)"
                    class="!text-gray-400 hover:!text-red-500 flex-shrink-0"
                  >
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
              <br />
              <button
                mat-raised-button
                class="!bg-blue-600 !text-white hover:!bg-blue-700 !px-4 !py-2 !rounded-lg !font-medium"
                (click)="addComment()"
              >
                Post Comment
              </button>

              <!-- Recursive Comment Thread Template -->
              <ng-template #commentThread let-comments>
                <ng-container *ngFor="let comment of comments">
                  <div class="mt-4 ml-4 p-3 bg-gray-100 rounded-lg">
                    <div class="flex items-center justify-between">
                      <p class="text-slateblue font-bold">
                        {{ comment.firstName }}
                      </p>
                      <span class="text-xs text-gray-500">{{
                        comment.createdAt | date : "short"
                      }}</span>
                    </div>
                    <p class="text-sm mt-1">{{ comment.commentText }}</p>
                    <!-- Show attachments if any -->
                    <div *ngIf="comment.attachments?.length" class="mt-2">
                      <div
                        *ngFor="let att of comment.attachments"
                        class="text-sm text-gray-700 flex items-center gap-2"
                      >
                        <mat-icon>{{ getFileIcon(att.fileName) }}</mat-icon>
                        <a
                          [href]="'http://localhost:3000/' + att.fileUrl"
                          target="_blank"
                          class="text-blue-600 underline"
                        >
                          {{ att.fileName }}
                        </a>
                      </div>
                    </div>
                    <!-- Reply Button -->
                    <button
                      class="text-sm text-blue-500 mt-1"
                      (click)="setReply(comment.commentId)"
                    >
                      Reply
                    </button>

                    <!-- Reply Input -->
                    <div
                      *ngIf="replyingToCommentId === comment.commentId"
                      class="mt-2 ml-4"
                    >
                      <textarea
                        [(ngModel)]="replyText"
                        placeholder="Write a reply..."
                        class="w-full p-2 border rounded-md"
                      ></textarea>
                      <!-- Reply Attachment Input -->

                      <div class="mt-2">
                        <label class="form-label text-sm text-gray-700"
                          >Reply Attachments</label
                        >
                        
                        <button  mat-raised-button

  class="!bg-blue-600 !text-white hover:!bg-blue-700 !px-4 !py-2 !rounded-lg !font-medium"
  (click)="fileInput.click()"
>
  Upload Files
</button>
<input
  #fileInput
  type="file"
  multiple
  class="hidden"
  (change)="onReplyFilesSelected($event, comment.commentId)"
/>


                        <!-- Selected Files List -->
                        <div
                          *ngIf="replySelectedFiles[comment.commentId]?.length"
                          class="mt-2 space-y-1"
                        >
                          <div
                            *ngFor="
                              let file of replySelectedFiles[comment.commentId];
                              let i = index
                            "
                            class="flex items-center justify-between bg-gray-100 p-2 rounded"
                          >
                            <span class="text-sm text-gray-700 truncate">{{
                              file.name
                            }}</span>
                            <button
                              (click)="removeReplyFile(comment.commentId, i)"
                            >
                              <mat-icon class="text-red-400">delete</mat-icon>
                            </button>
                          </div>
                        </div>
                      </div>
                      <br />
                      <div class="flex gap-2 mt-1">
                        <button
                          class="!bg-green-500 !text-white hover:!bg-green-600 !px-4 py-2 !rounded-lg !font-medium"
                         
                          (click)="submitReply(comment.commentId)"
                        >
                          Post Reply
                        </button>
                        <button
                          class="text-gray-500 text-sm"
                          (click)="replyingToCommentId = null"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>

                    <!-- Recursive Replies -->
                    <div
                      class="ml-4 border-l-2 pl-4 border-gray-300 mt-2"
                      *ngIf="comment.replies?.length"
                    >
                      <ng-container
                        *ngTemplateOutlet="
                          commentThread;
                          context: { $implicit: comment.replies }
                        "
                      ></ng-container>
                    </div>
                  </div>
                </ng-container>
              </ng-template>

              <!-- Start Rendering Comments -->
              <ng-container
                *ngTemplateOutlet="
                  commentThread;
                  context: { $implicit: comments }
                "
              ></ng-container>
            </div>

            <!-- Metadata -->
            <div class="section metadata">
              <div class="flex justify-between text-sm text-gray-500">
                <span
                  >Last Updated: {{ formatDate(ticket?.last_updated) }}</span
                >
                <span>Ticket ID: {{ ticket?.ticket_id }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
})
export class TicketDetailsComponent implements OnInit {
  ticket: Ticket | null = null;
  comments: any[] = [];
  newComment: string = "";
  replyingToCommentId: number | null = null;
  replyText: string = "";
  ticketId!: number;
  loggedInUserId!: number;
  selectedFiles: File[] = [];
  isDragging = false;
  replySelectedFiles: { [commentId: number]: File[] } = {};
  constructor(
    private ticketService: TicketService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      if (params["id"]) {
        this.loadTicket(params["id"]);
        this.ticketId = params["id"];
        this.loggedInUserId = Number(localStorage.getItem("userId"));
        this.loadComments(params["id"]);
      } else {
        this.router.navigate(["/tickets/list"]);
      }
    });
  }

  onReplyFilesSelected(event: Event, commentId: number) {
    const input = event.target as HTMLInputElement;
    if (!input.files) return;

    if (!this.replySelectedFiles[commentId]) {
      this.replySelectedFiles[commentId] = [];
    }

    Array.from(input.files).forEach((file) => {
      this.replySelectedFiles[commentId].push(file);
    });
  }

  removeReplyFile(commentId: number, index: number) {
    this.replySelectedFiles[commentId].splice(index, 1);
  }

  onFileSelected(event: Event): void {
    const files = (event.target as HTMLInputElement).files;
    if (files) {
      this.handleFiles(Array.from(files));
    }
  }
  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }
  handleFiles(files: File[]): void {
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = /\.(jpg|jpeg|png|pdf)$/i.test(file.name);
      return isValidSize && isValidType;
    });

    this.selectedFiles = [...this.selectedFiles, ...validFiles];
  }

  setReply(commentId: number) {
    this.replyingToCommentId = commentId;
    this.replyText = ""; // reset input each time
  }

  // submitReply(parentCommentId: number) {
  //   if (!this.replyText.trim()) return;

  //   const payload = {
  //     ticketId: this.ticketId,
  //     userId: this.loggedInUserId,
  //     parentCommentId,
  //     commentText: this.replyText,
  //   };

  //   this.ticketService.addComment(payload).subscribe(() => {
  //     this.replyingToCommentId = null;
  //     this.replyText = "";
  //     this.loadComments(this.ticketId);
  //   });
  // }
  submitReply(parentCommentId: number) {
    const text = this.replyText.trim();
    if (!text) return;

    const payload = {
      ticketId: this.ticketId,
      userId: this.loggedInUserId,
      parentCommentId,
      commentText: text,
    };

    this.ticketService.addComment(payload).subscribe((comment: any) => {
      const commentId = comment.commentId;

      const replyFiles = this.replySelectedFiles[parentCommentId];
      if (replyFiles?.length > 0) {
        const formData = new FormData();
        replyFiles.forEach((file) => formData.append("files", file));
        formData.append("commentId", commentId.toString());

        this.ticketService
          .uploadAttachmentsWithComment(formData)
          .subscribe(() => {
            this.cleanUpAfterReply(parentCommentId);
          });
      } else {
        this.cleanUpAfterReply(parentCommentId);
      }
    });
  }

  cleanUpAfterReply(parentCommentId: number) {
    this.replyingToCommentId = null;
    this.replyText = "";
    this.replySelectedFiles[parentCommentId] = [];
    this.loadComments(this.ticketId);
  }

  loadComments(id: number) {
    this.ticketService.getComments(id).subscribe(
      (data: Comment[]) => {
        //console.log("data", data);
        this.comments = this.groupComments(data);
      },
      (error) => {
        console.error("Error loading comments:", error);
      }
    );
  }

  groupComments(comments: Comment[]): Comment[] {
    const map = new Map<number, Comment>();
    const roots: Comment[] = [];

    comments.forEach((comment) => {
      map.set(comment.commentId, { ...comment, replies: [] });
    });

    map.forEach((comment) => {
      if (comment.parentCommentId) {
        const parent = map.get(comment.parentCommentId);
        if (parent) {
          parent.replies?.push(comment);
        }
      } else {
        roots.push(comment);
      }
    });

    return roots;
  }

  addComment() {
    const text = this.newComment.trim();
    if (!text) return;

    const payload = {
      ticketId: this.ticketId,
      userId: this.loggedInUserId,
      commentText: text,
      parentCommentId: undefined, // top-level comment
    };

    this.ticketService.addComment(payload).subscribe((comment: any) => {
      const commentId = comment.commentId;

      if (this.selectedFiles.length > 0) {
        const formData = new FormData();
        this.selectedFiles.forEach((file) => formData.append("files", file));
        formData.append("commentId", commentId.toString()); // 👈 Send with formData

        this.ticketService
          .uploadAttachmentsWithComment(formData)
          .subscribe(() => {
            this.resetFormAndReload();
          });
      } else {
        this.resetFormAndReload();
      }
    });
  }

  resetFormAndReload() {
    this.newComment = "";
    this.selectedFiles = [];
    this.loadComments(this.ticketId);
  }

  loadTicket(id: number) {
    this.ticketService.getTicketById(id).subscribe((ticket) => {
      if (ticket) {
        this.ticket = ticket;

        // Parse the 'tags' field (it should be a stringified JSON array)
        if (this.ticket.tags && typeof this.ticket.tags === "string") {
          this.ticket.tags = JSON.parse(this.ticket.tags);
        }

        // Parse the 'attachments' field (it should be a stringified JSON array)
        if (
          this.ticket.attachments &&
          typeof this.ticket.attachments === "string"
        ) {
          this.ticket.attachments = JSON.parse(this.ticket.attachments);
        }
      } else {
        this.router.navigate(["/tickets/list"]);
      }
    });
  }

  getTypeClass(type: string | undefined): string {
    switch (type?.toLowerCase()) {
      case "bug":
        return "bg-red-100 text-red-800";
      case "feature":
        return "bg-green-100 text-green-800";
      case "task":
        return "bg-blue-100 text-blue-800";
      case "improvement":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  getStatusClass(status: string | undefined): string {
    switch (status) {
      case "New":
        return "bg-blue-100 text-blue-800";
      case "In Progress":
        return "bg-yellow-100 text-yellow-800";
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "Closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  getPriorityClass(priority: string | undefined): string {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      case "Low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  getFileIcon(filename: string): string {
    if (/\.(jpg|jpeg|png)$/i.test(filename)) return "image";
    if (/\.pdf$/i.test(filename)) return "picture_as_pdf";
    return "insert_drive_file";
  }

  formatDate(date: string | undefined): string {
    if (!date) return "";
    return format(new Date(date), "MMM d, yyyy h:mm a");
  }

  editTicket() {
    if (this.ticket) {
      this.router.navigate(["/tickets/create"], {
        queryParams: { id: this.ticket.ticket_id },
      });
    }
  }

  goBack() {
    this.router.navigate(["/tickets/list"]);
  }
}
