{"name": "angular-starter", "private": true, "scripts": {"ng": "ng", "start": "concurrently \"ng serve\" \"node server/index.js\"", "build": "ng build", "server": "node server/index.js"}, "dependencies": {"@angular/animations": "^19.2.1", "@angular/cdk": "^18.1.0", "@angular/common": "^19.2.1", "@angular/compiler": "^19.2.1", "@angular/core": "^19.2.1", "@angular/forms": "^19.2.1", "@angular/material": "^18.1.0", "@angular/platform-browser": "^19.2.1", "@angular/router": "^19.2.1", "@ctrl/ngx-emoji-mart": "^9.2.0", "autoprefixer": "^10.4.17", "body-parser": "^1.20.2", "concurrently": "^8.2.2", "cors": "^2.8.5", "date-fns": "^3.3.1", "emoji-picker-element": "^1.26.3", "express": "^4.18.2", "ng-multiselect-dropdown": "^1.0.0", "ngx-toastr": "^19.0.0", "postcss": "^8.4.35", "rxjs": "^7.8.1", "tailwindcss": "^3.4.1", "tslib": "^2.5.0", "uuid": "^9.0.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^19.2.1", "@angular/cli": "^19.2.1", "@angular/compiler-cli": "^19.2.1", "typescript": "~5.5.0"}}