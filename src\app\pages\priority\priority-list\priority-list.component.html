<div class="container">
  <!-- Header -->
  <div class="header">
    <h1 class="text-3xl font-semibold text-gray-900">Priority</h1>
    <button class="add-btn" (click)="createPriority()">+ Add Priority</button>
  </div>

  <!-- Priority Table -->
  <div class="list-tags">
    <table class="curved-table">
      <thead>
        <tr>
          <th>Priority Name</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let priority of paginatedPriorities">
          <td>{{ priority.priorityName }}</td>
          <td>
            <span [ngClass]="{ 'status-pill': true, 'active': priority.isActive, 'inactive': !priority.isActive }">
              {{ priority.isActive ? 'Active' : 'Inactive' }}
            </span>
          </td>
          <td class="text-center">
            <button class="edit-btn" (click)="editPriority(priority.priorityId)">
              <mat-icon>edit</mat-icon> Edit
            </button>
            <button class="delete-btn" (click)="openDeleteDialog(priority.priorityId, priority.priorityName)">
              <mat-icon>delete</mat-icon> Delete
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination Controls -->
  <div class="pagination-container">
    <div class="pagination-left">
      <span class="text-sm text-gray-500">Show</span>
      <select
        [(ngModel)]="pageSize"
        (change)="handlePageEvent($event)"
        class="form-select text-sm text-gray-500"
      >
        <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }} items</option>
      </select>
    </div>

    <div class="pagination-right">
      <span class="pagination-info">
        {{ (currentPage * pageSize) + 1 }} - {{ Math.min((currentPage + 1) * pageSize, totalRecords) }} of {{ totalRecords }}
      </span>

      <div class="pagination-buttons">
        <button (click)="goToFirstPage()" [disabled]="currentPage === 0" class="pagination-arrow" title="First Page">
          <mat-icon>first_page</mat-icon>
        </button>
        <button (click)="goToPreviousPage()" [disabled]="currentPage === 0" class="pagination-arrow" title="Previous Page">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <ng-container *ngFor="let page of [].constructor(Math.ceil(totalRecords / pageSize)); let i = index">
          <button (click)="onPageChange(i)" [ngClass]="i === currentPage ? 'pagination-number active' : 'pagination-number'">
            {{ i + 1 }}
          </button>
        </ng-container>
        <button (click)="goToNextPage()" [disabled]="currentPage >= Math.ceil(totalRecords / pageSize) - 1" class="pagination-arrow" title="Next Page">
          <mat-icon>chevron_right</mat-icon>
        </button>
        <button (click)="goToLastPage()" [disabled]="currentPage >= Math.ceil(totalRecords / pageSize) - 1" class="pagination-arrow" title="Last Page">
          <mat-icon>last_page</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- DELETE CONFIRMATION MODAL -->
  <ng-template #deleteDialog>
    <div class="p-8 max-w-sm" [@fadeIn]>
      <div class="flex items-center mb-6">
        <div class="relative w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mr-4 animate-pulse">
          <mat-icon class="text-red-600 transform scale-125">warning</mat-icon>
          <div class="absolute inset-0 rounded-full border-4 border-red-200 animate-ping opacity-75"></div>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Delete Priority</h2>
      </div>

      <p class="text-gray-600 mb-6 leading-relaxed">
        Are you sure you want to delete this priority? This action is permanent and cannot be reversed.
      </p>

      <div class="bg-gray-50 rounded-xl p-4 mb-6 border border-gray-200 hover:border-red-200 transition-colors duration-200">
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-400 mr-2 text-sm">priority_high</mat-icon>
          <div class="text-sm text-gray-500">Priority ID: {{ selectedPriorityId }}</div>
        </div>
        <div class="font-medium text-gray-900 pl-6">{{ selectedPriorityName }}</div>
      </div>

      <p *ngIf="errorMessage" class="text-sm text-red-600 mb-4">{{ errorMessage }}</p>
      <p *ngIf="successMessage" class="text-sm text-green-600 mb-4">{{ successMessage }}</p>

      <div class="flex justify-end space-x-4">
        <button mat-button (click)="cancelDelete()" class="!px-6 !py-2 !rounded-lg !text-gray-700 hover:!bg-gray-100">
          <span class="flex items-center"><mat-icon class="!mr-2">close</mat-icon>Cancel</span>
        </button>
        <button mat-flat-button (click)="confirmDelete()" class="!px-6 !py-2 !rounded-lg !bg-red-600 hover:!bg-red-700 !text-white">
          <span class="flex items-center"><mat-icon class="!mr-2">delete</mat-icon>Delete</span>
        </button>
      </div>
    </div>
  </ng-template>

  <!-- FOREIGN KEY CONSTRAINT DIALOG -->
  <ng-template #fkConstraintDialog>
    <div class="p-8 max-w-sm" [@fadeIn]>
      <div class="flex items-center mb-6">
        <div class="relative w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mr-4 animate-pulse">
          <mat-icon class="text-yellow-600 transform scale-125">error_outline</mat-icon>
          <div class="absolute inset-0 rounded-full border-4 border-yellow-200 animate-ping opacity-75"></div>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Cannot Delete Priority</h2>
      </div>

      <p class="text-gray-600 mb-6 leading-relaxed">
        This priority is currently in use and cannot be deleted due to foreign key constraints.
      </p>

      <div class="bg-gray-50 rounded-xl p-4 mb-6 border border-gray-200 hover:border-yellow-200 transition-colors duration-200">
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-400 mr-2 text-sm">priority_high</mat-icon>
          <div class="text-sm text-gray-500">Priority ID: {{ selectedPriorityId }}</div>
        </div>
        <div class="font-medium text-gray-900 pl-6">{{ selectedPriorityName }}</div>
      </div>

      <div class="flex justify-end">
        <button mat-button (click)="cancelDelete()" class="!px-6 !py-2 !rounded-lg !text-gray-700 hover:!bg-gray-100">
          <span class="flex items-center"><mat-icon class="!mr-2">close</mat-icon>Close</span>
        </button>
      </div>
    </div>
  </ng-template>
</div>
