.container {
  max-width: 1500px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background: #f8f9fb;
  padding: 24px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  /* padding: 1.5rem;
  background-color: #f9fafb;
  font-family: 'Inter', sans-serif; */
}

.header {
 padding: 1.25rem;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h1 {
  font-size: 1.875rem; /* text-2xl */
  font-weight: 600;
  color: #111827; /* text-gray-900 */
}

.add-btn {
  background-color: #007bff;
 /* background-color: #457df7;   bg-blue-600 */
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
  border: none;
  cursor: pointer;
}

.add-btn:hover {
  background-color: #1d4ed8;
}

.list-tags {
  background-color: white;
  border-radius: 0.1rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.curved-table {
  
  width: 100%;
  border-collapse: collapse;
}
.curved-table th{
 background-color: #f3f3fe;
}

.curved-table th, .curved-table td {
 
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb; /* gray-200 */
  text-align: left;
}

.curved-table th {
  font-size: 11.5px;
 color: #6b7280; /* Deep blue-gray color matching the image */
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.06em;
  padding: 14px;
  background-color: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  text-align: left;
  /* font-size: 0.875rem;
  font-weight: 600;
  color: #b3b4b5; /* gray-500 */
  /* text-transform: uppercase; */ 
}

.curved-table td {
    background-color: #ffffff;
  padding: 14px;
  border-bottom: 1px solid #e5e7eb;
  color: #34495e;               /* Medium blue-gray for body text */
  font-weight: 400;             /* Normal */
  font-size: 14px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  /* font-size: 0.95rem;
  color: #111827; gray-900 */
}

.curved-table td.text-right {
  text-align: right;
}

.action-btn {
  background: transparent;
  border: none;
  cursor: pointer;
}

.menu-panel {
  min-width: 150px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 25px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  /* padding: 1.25rem 0 0 0; /* remove background and horizontal padding */
  /* display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
  background-color: transparent; remove background */ 
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: none;
  box-shadow: none;
}

.pagination-number {
  background: none;
  border: none;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  font-weight: 500;
}

.pagination-number.active {
  background-color: #bdcff7;
  color: white;
}

.pagination-number:hover:not(.active) {
  background-color: #f3f4f6;
}

.pagination-arrow {
  background: none;
  border: none;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  color: #374151;
  transition: background-color 0.2s ease-in-out;
}

.pagination-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-arrow:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.form-select {
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  font-size: 0.875rem;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
  margin-right: 1rem;
}


/* Status pill styles */
/* .status-pill {
   background-color: #fefcbf; /* Light yellow 
  color: #92400e;    
  padding: 0.25rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.status-active {
  background-color: #dcfce7;
  color: #15803d;
}

.status-inactive {
  background-color: #fef2f2;
  color: #b91c1c;
}

.status-pending {
  background-color: #fefce8;
  color: #92400e;
}

.status-completed{
   background-color: #facaca;
  color: #b91c1c;
}
 */

.status-pill {
  padding: 0.25rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  display: inline-block;
  text-transform: capitalize;
}

/* Known status styles */
.status-active {
  background-color: #dcfce7;
  color: #15803d;
}

.status-completed {
  background-color: #facaca;
  color: #b91c1c;
}

.status-pending {
  background-color: #fefce8;
  color: #92400e;
}

.status-cancelled {
  background-color: #fef2f2;
  color: #b91c1c;
}

/* Default (other) status style */
.status-default {
  background-color: #fefcbf; /* Light yellow */
  color: #92400e;            /* Dark yellow/brown for contrast */
}








.edit-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 5px 12px;
  margin-right: 5px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  outline: none;       /* Prevent outline */
  box-shadow: none;    /* Prevent shadow */
}

.delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 5px 12px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  outline: none;       /* Prevent outline */
  box-shadow: none;    /* Prevent shadow */
}

.edit-btn:hover {
  background-color: #0069d9;
  outline: none;
  box-shadow: none;
  border: none;
}

.delete-btn:hover {
  background-color: #c82333;
  outline: none;
  box-shadow: none;
  border: none;
}

.mat-icon {
  font-size: 18px;
  vertical-align: middle;
}
