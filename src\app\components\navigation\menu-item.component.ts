import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

import { RouterModule } from '@angular/router';
import { Menu } from '../../Models/Menu.model';


@Component({
  selector: 'app-menu-item',
  standalone: true,
  imports: [CommonModule, MatIconModule, RouterModule],
  template: `
  <!-- Menu item with children -->
<div *ngIf="item.children" class="px-3">
  <button 
    (click)="item.expanded = !item.expanded"
    class="flex items-center w-full px-3 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg"
  >
    <mat-icon class="mr-3 text-[20px]">{{item.icon}}</mat-icon>
    <span class="flex-1 text-sm font-medium">{{item.label}}</span>
    <mat-icon class="text-[20px] transform transition-transform duration-200" 
              [class.rotate-180]="item.expanded">
      expand_more
    </mat-icon>
  </button>

  <div *ngIf="item.expanded" class="ml-4 mt-1 space-y-0.5">
    <ng-container *ngFor="let child of item.children">
      <!-- Recursive rendering -->
      <app-menu-item [item]="child"></app-menu-item>
    </ng-container>
  </div>
</div>

<!-- Regular menu item -->
<a 
  *ngIf="!item.children"
  [routerLink]="[item.link]"
  routerLinkActive="bg-blue-50 text-blue-600"
  class="flex items-center px-6 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600"
>
  <mat-icon class="mr-3 text-[20px]">{{item.icon}}</mat-icon>
  <span class="flex-1 text-sm font-medium">{{item.label}}</span>
  <span 
    *ngIf="item.badge"
    [class]="getBadgeClass(item.badge.type)"
    class="text-xs px-2 py-0.5 rounded-full"
  >
    {{item.badge.text}}
  </span>
</a>

 
  `,
  styles: [`
    :host {
      display: block;
    }

    :host ::ng-deep .mat-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      line-height: 20px;
    }
  `]
})
export class MenuItemComponent {
  @Input() item!: Menu;


  getBadgeClass(type: string): string {
    switch (type) {
      case 'hot':
        return 'bg-red-100 text-red-600';
      case 'new':
        return 'bg-green-100 text-green-600';
      case 'beta':
        return 'bg-purple-100 text-purple-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  }
}