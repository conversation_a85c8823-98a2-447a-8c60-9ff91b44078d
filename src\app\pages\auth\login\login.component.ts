import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../../services/auth.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Component({
    selector: 'app-login',
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatInputModule,
        MatIconModule
    ],
    template: `
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <img src="https://placehold.co/64x64/6366F1/ffffff?text=D" 
             alt="Logo" 
             class="mx-auto h-16 w-16 rounded">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <a routerLink="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
            create a new account
          </a>
        </p>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- Error Message -->
            <div *ngIf="error" 
                 class="p-4 bg-red-50 border border-red-200 rounded-md">
              <p class="text-sm text-red-600">{{error}}</p>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div class="mt-1">
                <input id="email"
                       type="email"
                       formControlName="email"
                       class="form-input"
                       [class.error]="isFieldInvalid('email')"
                       placeholder="Enter your email">
                <div *ngIf="isFieldInvalid('email')" class="error-message">
                  Please enter a valid email address
                </div>
              </div>
            </div>

            <!-- Password -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div class="mt-1 relative">
                <input [type]="showPassword ? 'text' : 'password'"
                       id="password"
                       formControlName="password"
                       class="form-input pr-10"
                       [class.error]="isFieldInvalid('password')"
                       placeholder="Enter your password">
                <button type="button"
                        (click)="togglePasswordVisibility()"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <mat-icon class="text-gray-400">
                    {{showPassword ? 'visibility_off' : 'visibility'}}
                  </mat-icon>
                </button>
                <div *ngIf="isFieldInvalid('password')" class="error-message">
                  Password is required
                </div>
              </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input type="checkbox"
                       id="remember-me"
                       formControlName="rememberMe"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div class="text-sm">
                <a routerLink="/auth/forgot-password" class="font-medium text-blue-600 hover:text-blue-500">
                  Forgot your password?
                </a>
              </div>
            </div>

            <!-- Submit Button -->
            <div>
              <button type="submit"
                      [disabled]="loginForm.invalid || isLoading"
                      class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <mat-icon *ngIf="isLoading" class="animate-spin mr-2">refresh</mat-icon>
                {{isLoading ? 'Signing in...' : 'Sign in'}}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `
})
export class LoginComponent {
  loginForm: FormGroup;
  isLoading = false;
  error = '';
  showPassword = false;
  returnUrl: string = '/dashboard';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute, private http: HttpClient 
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
      rememberMe: [false]
    });

    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
  }

  isFieldInvalid(field: string): boolean {
    const formControl = this.loginForm.get(field);
    return !!formControl && formControl.invalid && formControl.touched;
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  // onSubmit(): void {
  //   if (this.loginForm.valid) {
  //     this.isLoading = true;
  //     this.error = '';

  //     const { email, password } = this.loginForm.value;

  //     this.authService.login(email, password).subscribe({
  //       next: () => {
          
  //         this.router.navigate([this.returnUrl]);
  //       },
  //       error: (error) => {
  //         this.error = error.error?.message || 'Invalid email or password';
  //         this.isLoading = false;
  //       }
  //     });
  //   } else {
  //     Object.keys(this.loginForm.controls).forEach(key => {
  //       const control = this.loginForm.get(key);
  //       if (control?.invalid) {
  //         control.markAsTouched();
  //       }
  //     });
  //   }
  // }


  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.error = '';

      const { email, password } = this.loginForm.value;

      this.authService.login(email, password).subscribe({
        next: (response: any) => {
          localStorage.setItem('userId', response.userId.toString());
          localStorage.setItem('token', response.token);
          
          console.log('Login Response:', response);

          const userId = response.userId;
          const token = response.token;
          const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

          this.http.get(`http://localhost:3000/api/ProfileMenu/profile/${userId}`, { headers })
            .subscribe({
              next: (profile) => {
                console.log('User Profile:', profile);
                localStorage.setItem('profile', JSON.stringify(profile));
                this.isLoading = false;
                this.router.navigate([this.returnUrl]);
              },
              error: (err) => {
                console.error('Failed to fetch profile:', err);
                this.isLoading = false;
                this.router.navigate([this.returnUrl]);
              }
            });
        },
        error: (error) => {
          this.error = error.error?.message || 'Invalid email or password';
          this.isLoading = false;
        }
      });
    }
  }
  

  
}