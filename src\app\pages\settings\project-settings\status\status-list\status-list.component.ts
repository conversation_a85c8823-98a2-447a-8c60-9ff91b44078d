
import { StatusService } from '../../../../../services/status.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Status } from '../../../../../Models/status';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatPaginatorModule } from '@angular/material/paginator';

@Component({
    selector: 'app-status-list',
    standalone: true,
    imports: [CommonModule, RouterModule, MatButtonModule, MatIconModule, MatMenuModule, FormsModule, MatPaginatorModule],
    templateUrl: './status-list.component.html',
    styleUrls: ['./status-list.component.css']
})
export class StatusListComponent implements OnInit {
    statuses: Status[] = [];
    paginatedStatuses: Status[] = [];

    pageSizeOptions: number[] = [5, 7, 10, 30];
    pageSize: number = 5;
    currentPage: number = 0;
    totalRecords: number = 0;
    totalPages: number = 0;

    constructor(
        private statusService: StatusService,
        private router: Router,
        private route: ActivatedRoute
    ) { }

    ngOnInit(): void {
        this.loadStatus();

        this.route.queryParams.subscribe(params => {
            if (params['statusid']) {
                console.log("Editing status with ID:", params['statusid']);
                // Call API if needed to fetch status details
            }
        });
    }

    loadStatus(): void {
        this.statusService.getStatuses().subscribe(
            (data: Status[]) => {
                console.log("Fetched statuses:", data);
                this.statuses = data;
                this.totalRecords = data.length;
                this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
                this.updatePaginatedStatus();
            },
            (error) => {
                console.error('Error fetching statuses', error);
            }
        );
    }

    updatePaginatedStatus(): void {
        const startIndex = this.currentPage * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, this.totalRecords);
        this.paginatedStatuses = this.statuses.slice(startIndex, endIndex);
    }

    onPageSizeChange(): void {
        this.currentPage = 0;
        this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
        this.updatePaginatedStatus();
    }

    firstPage(): void {
        this.currentPage = 0;
        this.updatePaginatedStatus();
    }

    lastPage(): void {
        this.currentPage = this.totalPages - 1;
        this.updatePaginatedStatus();
    }

    previousPage(): void {
        if (this.currentPage > 0) {
            this.currentPage--;
            this.updatePaginatedStatus();
        }
    }

    nextPage(): void {
        if (this.currentPage < this.totalPages - 1) {
            this.currentPage++;
            this.updatePaginatedStatus();
        }
    }

    goToPage(page: number): void {
        if (page >= 0 && page < this.totalPages) {
            this.currentPage = page;
            this.updatePaginatedStatus();
        }
    }

    get startIndex(): number {
        return this.currentPage * this.pageSize;
    }

    get endIndex(): number {
        return Math.min((this.currentPage + 1) * this.pageSize, this.totalRecords);
    }

    get visiblePages(): (string | number)[] {
        const totalPages = this.totalPages;
        const current = this.currentPage + 1;
        const pages: (string | number)[] = [];

        if (totalPages <= 7) {
            return Array.from({ length: totalPages }, (_, i) => i + 1);
        }

        if (current <= 3) {
            return [1, 2, 3, 4, "...", totalPages];
        }

        if (current >= totalPages - 2) {
            return [1, "...", totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
        }

        return [1, "...", current - 1, current, current + 1, "...", totalPages];
    }

    deleteStatus(statusid: number): void {
        if (confirm('Are you sure you want to delete this status?')) {
            this.statusService.deleteStatus(statusid).subscribe(() => {
                this.statuses = this.statuses.filter(status => status.statusid !== statusid);
                this.totalRecords = this.statuses.length;
                this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
                this.currentPage = Math.min(this.currentPage, this.totalPages - 1);
                this.updatePaginatedStatus();
                console.log('Status deleted successfully');
            });
        }
    }

    navigateToCreate() {
        this.router.navigate(['/settings/project/status/status-create']);
    }

    editStatus(status: Status): void {
        this.router.navigate(['/settings/project/status/status-edit', status.statusid]);
    }

    viewStatus(status: Status): void {
        this.router.navigate(['/status/details'], {
            queryParams: { statusid: status.statusid }
        });
    }
}