import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { TicketService, TicketType, TicketStatus, TicketPriority, Ticket, Assignee, Sprint, Tag, Attachment } from '../../../services/ticket.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown'; // Import the dropdown module
@Component({
    selector: 'app-ticket-create',
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatIconModule,
        MatInputModule,
        MatSelectModule,
      MatChipsModule,
      NgMultiSelectDropDownModule
    ],
    styles: [`
    :host {
      display: block;
      height: 100vh;
      overflow-y: auto;
      padding: 2rem;
      background-color: #F9FAFB;
    }

    .scroll-container {
      height: calc(100vh - 4rem);
      overflow-y: auto;
      padding-right: 1rem;
      margin-right: -1rem;
    }

    .form-container {
      position: relative;
      padding-bottom: 80px; /* Space for the fixed submit button */
    }

    .submit-container {
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      background: white;
      padding: 1rem;
      border-top: 1px solid #E5E7EB;
      margin: 0 -1.5rem -1.5rem -1.5rem;
      z-index: 10;
    }

    .file-drop-zone {
      border: 2px dashed #E5E7EB;
      border-radius: 0.5rem;
      padding: 2rem;
      text-align: center;
      transition: all 0.2s ease;
      cursor: pointer;
    }

    .file-drop-zone.dragover {
      border-color: #3B82F6;
      background-color: #EFF6FF;
    }
  `],
    template: `
    <div class="scroll-container">
      <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">{{isEditMode ? 'Edit' : 'Create'}} Ticket</h1>
              <p class="mt-1 text-sm text-gray-500">
                {{isEditMode ? 'Update the ticket information below' : 'Fill in the information below to create a new ticket'}}
              </p>
            </div>
            <button mat-button 
                    (click)="goBack()"
                    class="text-gray-600 hover:text-gray-900">
              <mat-icon class="!mr-2">arrow_back</mat-icon>
              Back to List
            </button>
          </div>
        </div>

        <!-- Form -->
        <form [formGroup]="ticketForm" 
              (ngSubmit)="onSubmit()" 
              class="p-6 form-container"
              (dragover)="onDragOver($event)"
              (dragleave)="onDragLeave($event)"
              (drop)="onDrop($event)">
          <!-- Title -->
          <div class="form-group">
            <label class="form-label">Ticket Title</label>
            <input
              type="text"
              formControlName="title"
              class="form-input"
              placeholder="Enter ticket title"
            >
            <div *ngIf="ticketForm.get('title')?.touched && ticketForm.get('title')?.invalid" 
                 class="error-message">
              Title is required
            </div>
          </div>

          <!-- Description -->
          <div class="form-group">
            <label class="form-label">Description</label>
            <textarea
              formControlName="description"
              class="form-input"
              rows="6"
              placeholder="Enter detailed description..."
            ></textarea>
            <div *ngIf="ticketForm.get('description')?.touched && ticketForm.get('description')?.invalid" 
                 class="error-message">
              Description is required
            </div>
          </div>

          <!-- Two Column Layout -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
              <!-- Type -->
              <div class="form-group">
                <label class="form-label">Type</label>
                <select formControlName="type" class="form-input">
                  <option value="null" disabled>Select type</option>
                  <option *ngFor="let type of ticketTypes" [value]="type.ticketTypeId">  {{ type.ticketTypeName }} </option>
                </select>
                <div *ngIf="ticketForm.get('type')?.touched && ticketForm.get('type')?.invalid" 
                     class="error-message">
                  Type is required
                </div>
              </div>

              <!-- Priority -->
              <div class="form-group">
                <label class="form-label">Priority</label>
                <select formControlName="priority" class="form-input">
                  <option value="null" disabled>Select priority</option>
                  <option *ngFor="let priority of priorities" [value]="priority.priorityId">{{priority.priorityName}}</option>
                </select>
                <div *ngIf="ticketForm.get('priority')?.touched && ticketForm.get('priority')?.invalid" 
                     class="error-message">
                  Priority is required
                </div>
              </div>

              <!-- Status -->
              <div class="form-group">
                <label class="form-label">Status</label>
                <select formControlName="status" class="form-input">
                  <option value="null" disabled>Select status</option>
                  <option *ngFor="let status of statuses" [value]="status.statusid">{{status.statusname}}</option>
                </select>
                <div *ngIf="ticketForm.get('status')?.touched && ticketForm.get('status')?.invalid" 
                     class="error-message">
                  Status is required
                </div>
              </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
              <!-- Assignee -->
              <div class="form-group">
                <label class="form-label">Assignee</label>
                <select formControlName="assignedTo" class="form-input">
                  <option value="null" disabled>Select assignee</option>
                  <option *ngFor="let member of teamMembers" [value]="member.userId">     {{ member.firstName }} {{member.lastName}}  </option>
                </select>
                <div *ngIf="ticketForm.get('assignedTo')?.touched && ticketForm.get('assignedTo')?.invalid" 
                     class="error-message">
                  Assignee is required
                </div>
              </div>

              <!-- Sprint -->
              <div class="form-group">
                <label class="form-label">Sprint</label>
                <select formControlName="sprint" class="form-input">
                  <option value="null" disabled>Select sprint</option>
                  <option *ngFor="let sprint of sprints" [value]="sprint.sprint_id">{{sprint.sprint_name}}</option>
                </select>
                <div *ngIf="ticketForm.get('sprint')?.touched && ticketForm.get('sprint')?.invalid" 
                     class="error-message">
                  Sprint is required
                </div>
              </div>

              <!-- Estimated Time -->
              <div class="form-group">
                <label class="form-label">Estimated Time (hours)</label>
                <input
                  type="number"
                  formControlName="estimatedTime"
                  class="form-input"
                  min="0"
                >
                <div *ngIf="ticketForm.get('estimatedTime')?.touched && ticketForm.get('estimatedTime')?.invalid" 
                     class="error-message">
                  <span *ngIf="ticketForm.get('estimatedTime')?.errors?.['required']">
                    Estimated time is required
                  </span>
                  <span *ngIf="ticketForm.get('estimatedTime')?.errors?.['min']">
                    Time must be positive
                  </span>
                </div>
              </div>
            </div>
          </div>
<br>
        <!-- Tags -->
        <div class="form-group">
  <label class="form-label">Tags</label>
  <ng-multiselect-dropdown 
    [placeholder]="'Select Tags'"
    [data]="tags"
    formControlName="selectedTags"
    [settings]="dropdownSettings"
    (onSelect)="onItemSelect($event)"
    (onSelectAll)="onSelectAll($event)">
  </ng-multiselect-dropdown>
</div>

         <!-- Attachments -->
<div class="form-group">
  <label class="form-label">Attachments</label>
  <div class="file-drop-zone"
       [class.dragover]="isDragging"
       (click)="fileInput.click()">
    <input #fileInput
           type="file"
           multiple
           class="hidden"
           (change)="onFileSelected($event)">
    <mat-icon class="text-gray-400 text-4xl mb-2">cloud_upload</mat-icon>
    <p class="text-sm text-gray-600 mb-1">
      <span class="text-blue-600 font-medium">Click to upload</span> or drag and drop
    </p>
    <p class="text-xs text-gray-500">
      PNG, JPG, PDF (max 10MB per file)
    </p>
  </div>

  <!-- File List -->
  <div *ngIf="selectedFiles.length > 0" 
       class="mt-4 space-y-2 max-h-48 overflow-y-auto">
    <div *ngFor="let file of selectedFiles; let i = index" 
         class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div class="flex items-center overflow-hidden">
        <mat-icon class="text-gray-400 mr-2 flex-shrink-0">
          {{getFileIcon(file.name)}}
        </mat-icon>
        <span class="text-sm text-gray-600 truncate">{{file.name}}</span>
        <!-- <span class="text-xs text-gray-400 ml-2">
          {{formatFileSize(file.size)}}
        </span> -->
      </div>
      <button type="button" 
              mat-icon-button 
              (click)="removeFile(i)"
              class="!text-gray-400 hover:!text-red-500 flex-shrink-0">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>
   <!-- Existing Attachments List -->
   <div *ngIf="existingAttachments.length > 0" 
       class="mt-4 space-y-2 max-h-48 overflow-y-auto">
    <div *ngFor="let attachment of existingAttachments; let i = index" 
         class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div class="flex items-center overflow-hidden">
        <mat-icon class="text-gray-400 mr-2 flex-shrink-0">
          {{getFileIcon(attachment.file_name)}}
        </mat-icon>
        <span class="text-sm text-gray-600 truncate">{{attachment.file_name}}</span>
      </div>
      <!-- Optionally, add a button to remove the file -->
      <button type="button" 
              mat-icon-button 
              (click)="removeExistingAttachment(i)"
              class="!text-gray-400 hover:!text-red-500 flex-shrink-0">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>
</div>

          <!-- Submit Button Container -->
          <div class="submit-container">
            <div class="max-w-5xl mx-auto flex justify-end">
              <button type="submit"
                      mat-raised-button
                      color="primary"
                      [disabled]="!ticketForm.valid"
                      class="!px-6 !py-2">
                <span class="flex items-center">
                  <mat-icon class="!mr-2">{{isEditMode ? 'update' : 'add'}}</mat-icon>
                  {{isEditMode ? 'Update' : 'Create'}} Ticket
                </span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  `
})
  
export class TicketCreateComponent implements OnInit {
  existingAttachments: any[] = [];  // Initialize it as an empty array for now
  ticketForm: FormGroup;
  ticketTypes: TicketType[] = [];
  priorities: TicketPriority[] = [];
  statuses: TicketStatus[] = [];
  teamMembers: Assignee[] = [];
  sprints: Sprint[] = [];
  tags: Tag[] = [];
  selectedFiles: File[] = [];
  readonly separatorKeysCodes = [ENTER, COMMA] as const;
  isDragging = false;
  isEditMode = false;
  ticketId: number | null = null;
  selectedTags: Tag[] = []; // To hold the selected tags
  dropdownSettings = {
    singleSelection: false,
    idField: 'tag_id', // ID field to identify each tag
    textField: 'tag_name', // Display the tagName in the dropdown
    selectAllText: 'Select All',
    unSelectAllText: 'Unselect All',
    //itemsShowLimit: 3,
    allowSearchFilter: true
  };
  constructor(
    private fb: FormBuilder,
    private ticketService: TicketService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.ticketForm = this.createForm();
  }

  ngOnInit() {
    this.fetchTicketTypes();
    this.getTicketPriorities();
    this.loadTeamMembers();
    this.getAllTicketStatus();
    this.loadSprints();
    this.loadTags();
    // Check for ticket ID in route params for edit mode
    this.route.queryParams.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.ticketId = params['id'];
        this.loadTicket(params['id']);
      }
    });
  }
  loadTags(): void {
    this.ticketService.getTags().subscribe(
      (data: Tag[]) => {
        //console.log('Tags received from API:', data);
        this.tags = data; // Populate the dropdown with fetched tags
        //console.log('Tags assigned to this.tags:', this.tags);
      },
      (error) => {
        console.error('Error fetching tags', error);
      }
    );
  }


  onItemSelect(item: any) {
    const selectedTag: Tag = { tag_id: item.tag_id, tag_name: item.tag_name };
    
    if (!this.selectedTags.some(tag => tag.tag_id === selectedTag.tag_id)) {
      this.selectedTags.push(selectedTag);
    }
    console.log('Selected Tags:', this.selectedTags);
  }
  
  onSelectAll(items: any[]) {
    this.selectedTags = items.map(item => ({
      tag_id: item.tag_id,
      tag_name: item.tag_name
    }));
    console.log('All Selected Tags:', this.selectedTags);
  }

  fetchTicketTypes() {
    this.ticketService.getTicketTypes().subscribe(types => {
      console.log(types);
      this.ticketTypes = types;
    });
  }

  getAllTicketStatus() {
    this.ticketService.getAllTicketStatus().subscribe(status => {
      this.statuses = status;
    });
  }


  getTicketPriorities() {
    this.ticketService.getAllPriorities().subscribe(priority => {
      this.priorities = priority;
    });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', Validators.required],
      description: ['', Validators.required],
      type: [null, Validators.required],
      priority: [null, Validators.required],
      status: [null, Validators.required],
      assignedTo: [null, Validators.required],
      sprint: [null, Validators.required],
      estimatedTime: [0, [Validators.required, Validators.min(0)]],
      selectedTags: [[]],  // ✅ Add selectedTags as a FormControl
      existingAttachments: [[]] // Ensure this is added
    });
  }

  private loadTicket(id: number) {
    this.ticketService.getTicketById(id).subscribe(ticket => {
      if (ticket) {
        // Parse tags and attachments (since they're stringified JSON in the API response)
        const tagsArray = Array.isArray(ticket.tags) ? ticket.tags : JSON.parse(ticket.tags || '[]');
        const attachmentsArray = Array.isArray(ticket.attachments) ? ticket.attachments : JSON.parse(ticket.attachments || '[]');
        
        // Update the form with ticket details
        this.ticketForm.patchValue({
          title: ticket.title,
          description: ticket.description,
          type: ticket.type_id,
          priority: ticket.priority_id,
          status: ticket.statusid,
          assignedTo: ticket.userid,
          sprint: ticket.sprint_id,
          estimatedTime: ticket.estimated_time,
        });
        
        // Set selected tags for multi-select dropdown
        this.selectedTags = tagsArray.map((tag: { tag_id: number, tag_name: string }) => ({
          tag_id: tag.tag_id,
          tag_name: tag.tag_name
        }));
  
        // Bind the selected tags to the form control
        this.ticketForm.get('selectedTags')?.setValue(this.selectedTags);
  
        // Handle attachments if any (file_name, file_url, file_size)
        if (attachmentsArray.length > 0) {
       
  
          // Set existing attachments for form submission
          this.existingAttachments = attachmentsArray.map((att: { attachment_id: number, file_name: string, file_url: string, uploaded_at: string }) => ({
            attachment_id: att.attachment_id,  // Store attachment_id for reference when updating
            file_name: att.file_name,          // File name for display
            file_url: att.file_url,            // URL for the file
            uploaded_at: att.uploaded_at      // Upload date for reference
          }));
          this.selectedFiles = [];
          // Bind the existing attachments to the form control (if applicable)
          this.ticketForm.get('existingAttachments')?.setValue(this.existingAttachments);
        }
  
        //console.log('Loaded Ticket:', ticket);
        //console.log('Selected Tags:', this.selectedTags);
        //console.log('Selected Files:', this.selectedFiles);
        //console.log('Existing Attachments:', this.existingAttachments); // Log existing attachments
      }
    });
  }
  
  loadTeamMembers() {
    this.ticketService.getTeamMembers().subscribe(members => {
   
      this.teamMembers = members;
  
    
    });
  }
  
  
  loadSprints() {
    this.ticketService.getSprints().subscribe(sprints => {
      this.sprints = sprints;
    });
  }

  addTag(event: MatChipInputEvent): void {
    const input = event.input;
    const value = event.value;
  
    if ((value || '').trim()) {
      // Creating a new Tag object instead of just a string
      const newTag: Tag = { tag_id: 0, tag_name: value.trim() };
      this.tags.push(newTag);
    }
  
    if (input) {
      input.value = '';
    }
  }
  
  removeTag(tag: Tag): void {
    const index = this.tags.indexOf(tag);
    if (index >= 0) {
      this.tags.splice(index, 1);
    }
  }

  toggleTagSelection(tag: Tag): void {
    const index = this.selectedTags.findIndex(t => t.tag_id === tag.tag_id);
    if (index >= 0) {
      this.selectedTags.splice(index, 1); // Remove if already selected
    } else {
      this.selectedTags.push(tag); // Add new selection
    }
  }
  
  isTagSelected(tag: Tag): boolean {
    return this.selectedTags.some(t => t.tag_id === tag.tag_id);
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
    
    const files = event.dataTransfer?.files;
    if (files) {
      this.handleFiles(Array.from(files));
    }
  }

  onFileSelected(event: Event): void {
    const files = (event.target as HTMLInputElement).files;
    if (files) {
      this.handleFiles(Array.from(files));
    }
  }
  removeExistingAttachment(index: number): void {
    // Remove the attachment at the specified index from the existingAttachments array
    this.existingAttachments.splice(index, 1);
    
    // Update the form value (this ensures that the UI is consistent with the form)
    this.ticketForm.get('existingAttachments')?.setValue(this.existingAttachments);
    
    // Optionally, log the updated existingAttachments array
    console.log('Updated Existing Attachments:', this.existingAttachments);
  }
  

  handleFiles(files: File[]): void {
    // Filter files by size and type
    const validFiles = files.filter(file => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = /\.(jpg|jpeg|png|pdf)$/i.test(file.name);
      return isValidSize && isValidType;
    });

    this.selectedFiles = [...this.selectedFiles, ...validFiles];
  }

  getFileIcon(filename: string): string {
    if (/\.(jpg|jpeg|png)$/i.test(filename)) return 'image';
    if (/\.pdf$/i.test(filename)) return 'picture_as_pdf';
    return 'insert_drive_file';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  onSubmit(): void {
    if (this.ticketForm.valid) {
      const formValues = this.ticketForm.value;
  
      const ticketData = {
        Title: formValues.title?.trim() || '',
        Description: formValues.description?.trim() || '',
        TypeId: this.parseNumber(formValues.type),
        PriorityId: this.parseNumber(formValues.priority),
        StatusId: this.parseNumber(formValues.status),
        UserId: this.parseNumber(formValues.assignedTo),
        SprintId: this.parseNumber(formValues.sprint),
        EstimatedTime: this.parseNumber(formValues.estimatedTime, 0),
        Tags: formValues.selectedTags.map((tag: any) => tag.tag_id),  // Send tag IDs
        Attachments: this.selectedFiles,  // New attachments
        // ExistingAttachments: this.existingAttachments,  // Existing attachments metadata
        ExistingAttachments: Array.isArray(this.existingAttachments) ? this.existingAttachments : [this.existingAttachments],  // **Force array**
      };
      console.log('Ticket Data:', ticketData);  // Debug log to check what is actually being sent
      // Create FormData for the backend
      const formData = new FormData();
      formData.append('Title', ticketData.Title);
      formData.append('Description', ticketData.Description);
  
        // Append TypeId, PriorityId, StatusId, UserId, SprintId, EstimatedTime
      // Only append non-null values to avoid errors
      if (ticketData.TypeId !== null) formData.append('TypeId', ticketData.TypeId.toString());
      if (ticketData.PriorityId !== null) formData.append('PriorityId', ticketData.PriorityId.toString());
      if (ticketData.StatusId !== null) formData.append('StatusId', ticketData.StatusId.toString());
      if (ticketData.UserId !== null) formData.append('UserId', ticketData.UserId.toString());
      if (ticketData.EstimatedTime !== null) formData.append('EstimatedTime', ticketData.EstimatedTime.toString());
      if (ticketData.SprintId !== null) formData.append('SprintId', ticketData.SprintId.toString());
      // Append tags as individual form entries
      // ticketData.Tags.forEach((tagId: number) => {
      //   formData.append('Tags', tagId.toString());
      // });
  
      if (ticketData.Tags.length > 0) {
        ticketData.Tags.forEach((tagId: number) => {
          formData.append('Tags', tagId.toString());
        });
      } else {
        formData.append('Tags', ""); // Ensure an empty value is sent
      }

      
      // Append new attachments (files)
      ticketData.Attachments.forEach(file => {
        // Ensure that only File objects are appended here
        if (file instanceof File) {
          formData.append('Attachments', file, file.name);
        }
      });
  
      console.log('ticketData.ExistingAttachments.length', ticketData.ExistingAttachments.length);
     

      if (ticketData.ExistingAttachments && ticketData.ExistingAttachments.length > 0) {
        const attachmentsArray = Array.isArray(ticketData.ExistingAttachments)
          ? ticketData.ExistingAttachments
          : [ticketData.ExistingAttachments];
        const attachmentsJson = JSON.stringify(attachmentsArray);
        formData.append('ExistingAttachments', attachmentsJson);
      }

      console.log('TypeId:', this.parseNumber(formValues.type));
      console.log('PriorityId:', this.parseNumber(formValues.priority));
      
  // Check the contents of FormData in the console
formData.forEach((value, key) => {
  console.log(`${key}:`, value);
});
      // Call the appropriate API method for create/update
      const request = this.isEditMode && this.ticketId
        ? this.ticketService.updateTicketWithAttachments(this.ticketId, formData)
        : this.ticketService.createTicketWithAttachments(formData);
  
      request.subscribe({
        next: (newTicket) => {
          console.log('Ticket created successfully:', newTicket);
      
          // Fetch updated tickets list after creation
          this.ticketService.getTickets().subscribe((updatedTickets) => {
            this.ticketService.updateTicketsState(updatedTickets); // ✅ Update the BehaviorSubject
            this.router.navigate(['/tickets/list']);
          });
        },
        error: (error) => console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} ticket:`, error)
      });
    }
  }
  
  
  
  
  
  // Convert empty, undefined, or invalid values to null or numbers
  parseNumber(value: any, defaultValue: number | null = null): number | null {
    const parsedValue = Number(value);
    return !isNaN(parsedValue) && value !== "" ? parsedValue : defaultValue;
  }
  

  goBack(): void {
    this.router.navigate(['/tickets/list']);
  }
}