import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
    selector: 'app-delete-confirmation-dialog',
    imports: [CommonModule, MatDialogModule, MatButtonModule, MatIconModule],
    animations: [
        trigger('fadeIn', [
            transition(':enter', [
                style({ opacity: 0, transform: 'scale(0.95)' }),
                animate('150ms ease-out', style({ opacity: 1, transform: 'scale(1)' }))
            ])
        ])
    ],
    template: `
    <div class="p-8 max-w-sm" [@fadeIn]>
      <!-- Header with animated warning icon -->
      <div class="flex items-center mb-6">
        <div class="relative w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mr-4 animate-pulse">
          <mat-icon class="text-red-600 transform scale-125">warning</mat-icon>
          <div class="absolute inset-0 rounded-full border-4 border-red-200 animate-ping opacity-75"></div>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Delete Ticket</h2>
      </div>

      <!-- Content with improved typography -->
      <p class="text-gray-600 mb-6 leading-relaxed">
        Are you sure you want to delete this ticket? This action is permanent and cannot be reversed.
      </p>

      <!-- Ticket Info with enhanced styling -->
      <div class="bg-gray-50 rounded-xl p-4 mb-8 border border-gray-200 hover:border-red-200 transition-colors duration-200">
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-400 mr-2 text-sm">label</mat-icon>
          <div class="text-sm text-gray-500">Ticket ID: {{data.ticket.id}}</div>
        </div>
        <div class="font-medium text-gray-900 pl-6">{{data.ticket.title}}</div>
      </div>

      <!-- Actions with improved buttons -->
      <div class="flex justify-end space-x-4">
        <button mat-button
                (click)="onCancel()"
                class="!px-6 !py-2 !rounded-lg !text-gray-700 hover:!bg-gray-100 !transition-colors !duration-200">
          <span class="flex items-center">
            <mat-icon class="!mr-2">close</mat-icon>
            Cancel
          </span>
        </button>
        <button mat-flat-button
                (click)="onConfirm()"
                class="!px-6 !py-2 !rounded-lg !bg-red-600 hover:!bg-red-700 !text-white !transition-colors !duration-200">
          <span class="flex items-center">
            <mat-icon class="!mr-2">delete</mat-icon>
            Delete
          </span>
        </button>
      </div>
    </div>
  `,
    styles: [`
    :host {
      display: block;
    }
    
    :host ::ng-deep .mat-mdc-dialog-container {
      padding: 0 !important;
    }

    .animate-pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: .7;
      }
    }

    .animate-ping {
      animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
    }

    @keyframes ping {
      75%, 100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }
  `]
})
export class DeleteConfirmationDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<DeleteConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { ticket: { id: string; title: string; } }
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }
}