import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Status } from '../Models/status'; 
@Injectable({
  providedIn: 'root'
})
export class StatusService {
  private apiUrl = 'http://localhost:3000/api/status'; //

  constructor(private http: HttpClient) {}

  getStatuses(): Observable<Status[]> {
    return this.http.get<Status[]>(`${this.apiUrl}/AllStatus`); // ✅ matches working endpoint
  }

  getStatus(id: number): Observable<Status> {
    return this.http.get<Status>(`${this.apiUrl}/getstatusbyid/${id}`);
  }
  
  addStatus(status: Status): Observable<Status> {
    return this.http.post<Status>('http://localhost:3000/api/status/AddStatus', status);
  }
  updateStatus(id: number, status: Status): Observable<Status> {
    return this.http.put<Status>(`http://localhost:3000/api/status/${id}`, status);
  }
  
  deleteStatus(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
export { Status };

