<div class="scroll-container">
  <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-lg font-semibold text-gray-900">
            {{ isEditMode ? 'Edit' : 'Create' }} Priority
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEditMode ? 'Update the priority information below' : 'Fill in the information below to create a new priority' }}
          </p>
        </div>
       
     <button mat-button (click)="goBack()" class="back-button">
          <mat-icon class="text-lg">arrow_back</mat-icon>
          <span>Back to List</span>
        </button>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="priorityForm" (ngSubmit)="onSubmit()" class="p-6 form-container">
      <div class="form-group">
        <label class="form-label">Priority Name</label>
      <input
          type="text"
          formControlName="priorityName"
          class="form-input"
          placeholder="Enter Priority Name"
        />

        <div *ngIf="priorityForm.get('priorityName')?.touched && priorityForm.get('priorityName')?.invalid" class="error-message">
          Priority Name is required
        </div>
      </div>

      <!-- Status Toggle -->
<label class="form-label flex items-center gap-2 cursor-pointer">
  Status
  <input
    type="checkbox"
    formControlName="isActive"
    id="isActive"
    class="form-checkbox h-5 w-5 text-blue-600 ml-4"
  />
</label>

<!-- Button Container for Submit -->
<div class="button-container">
  <button
    type="submit"
    [disabled]="priorityForm.invalid"
    class="right-button"
    [title]="priorityForm.invalid ? 'Please enter required fields' : ''">
    
    <mat-icon>{{ isEditMode ? 'update' : 'add' }}</mat-icon>
    {{ isEditMode ? ' Update Priority' : ' Create Priority' }}
  </button>
</div>

    </form>
  </div>
</div>
