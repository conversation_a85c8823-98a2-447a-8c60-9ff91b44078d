<div class="scroll-container">
    <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm mt-20">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">{{isEditMode ? 'Edit' : 'Create'}} Status</h1>
            <p class="mt-1 text-sm text-gray-500">
              {{isEditMode ? 'Update the status information below' : 'Fill in the information below to create a new status'}}
            </p>
          </div>
        
            <button 
            class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition"
            (click)="goBack()"
        >
            <mat-icon class="mr-2">arrow_back</mat-icon>
            Back to List
        </button>
        </div>
      </div>
         <!-- Form -->
         <form [formGroup]="statusForm" (ngSubmit)="onStatusSubmit()" class="p-6 form-container"> 
     <!-- StatusName -->
     <div class="form-group">
      <label class="form-label">Status Name</label>
      <textarea
        formControlName="statusname"
        class="form-input"
        placeholder="Enter Status name..."
      ></textarea>
      <div *ngIf="statusForm.get('statusname')?.touched && statusForm.get('statusname')?.invalid" class="error-message">
        Status Name is required
      </div>
      
        <!-- Duplicate Status Name Error (Hidden by Default) -->
        <div *ngIf="duplicateStatusError && statusForm.get('statusname')?.value" class="error-message">
          This status name already exists. Please enter a different name.
        </div>
</div>
<!-- Submit button container -->
    <div class="submit-container">
        <div class="max-w-5xl mx-auto flex justify-end">
          <button
            type="submit"
            mat-raised-button
            color="primary"
            [disabled]="!statusForm.valid"
            class="px-6 py-2 rounded-full bg-gray-200 text-gray-700 font-medium shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-75 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
          >
            <span class="flex items-center">
              <mat-icon class="mr-2">{{ isEditMode ? 'update' : 'add' }}</mat-icon>
              {{ isEditMode ? 'Update' : 'Create' }} Status
            </span>
          </button>
        </div>
      </div>