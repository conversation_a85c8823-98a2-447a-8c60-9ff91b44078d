import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { RoleService } from '../../../../../services/role.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-role-create',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatIconModule],
  templateUrl: './role-create.component.html',
  styleUrls: ['./role-create.component.css'],
})
export class RoleCreateComponent implements OnInit {
  roleForm!: FormGroup;
  isEditMode = false;
  roleId!: number;
  roleNameExists = false;
  loading = false;
  roles: { roleId: number, roleName: string }[] = []; // local roles list

  constructor(
    private fb: FormBuilder,
    private roleService: RoleService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.roleForm = this.fb.group({
      roleName: ['', Validators.required],
      isActive: [true], // ✅ This line fixes the error
    });

    // Load roles once on init
    this.roleService.getRoles().subscribe((data) => {
      this.roles = data;
    });

    this.route.params.subscribe((params) => {
      if (params['Id']) {
        this.isEditMode = true;
        this.roleId = +params['Id'];
        this.loadRole(this.roleId);
      }
    });

    // Watch for changes to check duplication
    this.roleForm.get('roleName')?.valueChanges.subscribe(() => {
      this.checkRoleNameExists();
    });
  }

  loadRole(id: number): void {
    this.roleService.getRoleById(id).subscribe((role) => {
      this.roleForm.patchValue({ roleName: role.roleName });
    });
  }

  checkRoleNameExists(): void {
    const roleName = this.roleForm.get('roleName')?.value?.trim().toLowerCase();
    if (!roleName) {
      this.roleNameExists = false;
      return;
    }

    const duplicate = this.roles.find((role) => {
      const match = role.roleName.toLowerCase() === roleName;
      return this.isEditMode ? match && role.roleId !== this.roleId : match;
    });

    this.roleNameExists = !!duplicate;
  }

  onSubmit(): void {
    if (this.roleForm.invalid || this.roleNameExists) return;

    const role = this.roleForm.value;
    this.loading = true;

    if (this.isEditMode) {
      this.roleService.updateRole(this.roleId, role).subscribe({
        next: () => this.router.navigate(['/settings/project/roles/role-list']),
        error: (err) => {
          console.error('Error updating role:', err);
          this.loading = false;
        },
      });
    } else {
      this.roleService.createRole(role).subscribe({
        next: () => this.router.navigate(['/settings/project/roles/role-list']),
        error: (err) => {
          console.error('Error creating role:', err);
          this.loading = false;
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/settings/project/roles/role-list']);
  }
}