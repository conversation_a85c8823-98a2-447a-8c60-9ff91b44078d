import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HeaderComponent } from '../components/navigation/header.component';
import { SidebarMenuComponent } from '../components/navigation/sidebar-menu.component';
import { MenuService } from '../services/menu.service';
import { Observable } from 'rxjs';
import { Menu } from '../Models/Menu.model';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeaderComponent,
    SidebarMenuComponent
  ],
  template: `
    <div class="flex h-screen bg-gray-100 overflow-hidden">
      <!-- Sidebar -->
      <div [class.translate-x-0]="isSidebarOpen" 
           [class.-translate-x-full]="!isSidebarOpen"
           class="fixed inset-y-0 left-0 z-30 transform lg:relative lg:translate-x-0 transition duration-200 ease-in-out">
        <app-sidebar-menu [menuItems]="menuItems$ | async"></app-sidebar-menu>
      </div>

      <!-- Overlay for mobile -->
      <div *ngIf="isSidebarOpen" 
           class="fixed inset-0 bg-gray-600 bg-opacity-50 z-20 lg:hidden"
           (click)="toggleSidebar()">
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col min-w-0">
        <app-header (menuToggle)="toggleSidebar()"></app-header>
        <main class="flex-1 overflow-y-auto">
          <router-outlet></router-outlet>
        </main>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      height: 100vh;
    }
  `]
})
export class MainLayout implements OnInit {
  userRoleId: number | null = null;  
  menuItems$!: Observable<Menu[]>;   
  isSidebarOpen = false;

  constructor(private menuService: MenuService) {}

  ngOnInit() {
    this.loadUserRole();
  }

  loadUserRole() {
    this.userRoleId = this.getUserRole();
    this.menuItems$ = this.menuService.getMenuItems(this.userRoleId);
  }

  getUserRole(): number {
    const roleIdString = localStorage.getItem('role');
    if (roleIdString) {
      const roleId = parseInt(roleIdString, 10);
      return isNaN(roleId) ? 1 : roleId;
    }
    return 1;
  }

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }
}
