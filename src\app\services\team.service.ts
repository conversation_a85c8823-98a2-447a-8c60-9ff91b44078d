import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Team } from '../Models/Team.model';

@Injectable({
  providedIn: 'root'
})
export class TeamService {
  private apiUrl = 'http://localhost:3000/api/teams'; // adjust if needed

  constructor(private http: HttpClient) {}

  getAllTeams(): Observable<any[]> {
    return this.http.get<any[]>(this.apiUrl);
  }
  getTeamById(id: number): Observable<Team> {
    return this.http.get<Team>(`${this.apiUrl}/${id}`);
  }

  createTeam(team: Team): Observable<any> {
    return this.http.post(`${this.apiUrl}`, team);
  }

  updateTeam(id: number, team: Team): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, team);
  }

 
}
