import { Component, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthService } from '../../services/auth.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule
  ],
  template: `
    <header class="bg-white shadow-sm">
      <div class="flex items-center justify-between px-4 lg:px-6 py-4">
        <!-- Left Section -->
        <div class="flex items-center">
          <button mat-icon-button 
                  class="lg:mr-4"
                  matTooltip="Toggle Menu"
                  (click)="onMenuToggle()">
            <mat-icon>menu</mat-icon>
          </button>
          <div class="hidden md:flex relative">
            <mat-icon class="absolute left-3 top-2.5 text-gray-400">search</mat-icon>
            <input type="text" 
                   placeholder="Search..." 
                   class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>
        
        <!-- Right Section -->
        <div class="flex items-center space-x-2 md:space-x-4">
          <button mat-icon-button class="md:hidden" (click)="toggleMobileSearch()">
            <mat-icon>search</mat-icon>
          </button>

          <button mat-icon-button [matMenuTriggerFor]="notificationsMenu" matTooltip="Notifications" class="relative">
            <mat-icon>notifications</mat-icon>
            <span class="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
              3
            </span>
          </button>
          <mat-menu #notificationsMenu="matMenu" class="notifications-menu" xPosition="before">
            <div class="p-2 w-280 max-w-[190vw]">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Notifications</h3>
                <button mat-button color="primary" class="!text-sm">Mark all as read</button>
              </div>
              <div class="space-y-4">
                <div *ngFor="let notification of notifications" class="flex items-start p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div [class]="'w-8 h-8 rounded-full flex items-center justify-center mr-3 ' + notification.iconBg">
                    <mat-icon [class]="notification.iconColor">{{notification.icon}}</mat-icon>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900">{{notification.title}}</p>
                    <p class="text-sm text-gray-500 truncate">{{notification.message}}</p>
                    <p class="text-xs text-gray-400 mt-1">{{notification.time}}</p>
                  </div>
                </div>
              </div>
              <mat-divider class="my-4"></mat-divider>
              <button mat-button color="primary" class="w-full !justify-center" (click)="viewAllNotifications()">
                View All Notifications
              </button>
            </div>
          </mat-menu>

          <!-- Dynamic Profile Menu -->
          <div class="relative">
            <button mat-button [matMenuTriggerFor]="profileMenu" class="!px-2 !py-1 hover:!bg-gray-50 !rounded-lg">
              <div class="flex items-center">
                <img [src]="getProfileImage()" alt="Profile" class="h-8 w-8 rounded-full">
                <div class="hidden md:block ml-2 text-left">
                  <div class="text-sm font-medium text-gray-900">{{ profileData.fullName }}</div>
                  <div class="text-xs text-gray-500">{{ profileData.roleName }}</div>
                </div>
                <mat-icon class="ml-2 text-gray-400">expand_more</mat-icon>
              </div>
            </button>

            <mat-menu #profileMenu="matMenu" class="profile-menu" xPosition="before">
              <div class="p-4 w-[320px] max-w-[90vw]">
                <div class="flex items-center space-x-4 mb-6">
                  <div class="relative">
                    <img [src]="getProfileImage(64)" alt="Profile" class="w-16 h-16 rounded-full">
                    <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  </div>
                  <div>
                    <h2 class="text-lg font-semibold text-gray-900">{{ profileData.fullName }}</h2>
                    <p class="text-sm text-gray-500">{{ profileData.roleName }}</p>
                    <p class="text-sm text-gray-500">{{ profileData.email }}</p>
                  </div>
                </div>

                <div class="grid grid-cols-3 gap-3 mb-6">
                  <button class="flex flex-col items-center justify-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                          (click)="navigateTo('/settings/profile')">
                    <mat-icon class="text-gray-600 mb-1">account_circle</mat-icon>
                    <span class="text-xs text-gray-600">Profile</span>
                  </button>
                  <button class="flex flex-col items-center justify-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                          (click)="navigateTo('/settings')">
                    <mat-icon class="text-gray-600 mb-1">settings</mat-icon>
                    <span class="text-xs text-gray-600">Settings</span>
                  </button>
                  <button class="flex flex-col items-center justify-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                          (click)="navigateTo('/notifications')">
                    <mat-icon class="text-gray-600 mb-1">notifications</mat-icon>
                    <span class="text-xs text-gray-600">Alerts</span>
                  </button>
                </div>

                <div class="space-y-1">
                  <button mat-button class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                          (click)="navigateTo('/dashboard')">
                    <mat-icon class="!mr-3 text-gray-500">dashboard</mat-icon>
                    <span class="text-gray-700">Dashboard</span>
                  </button>

                  <button mat-button class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                          (click)="navigateTo('/tickets/list')">
                    <mat-icon class="!mr-3 text-gray-500">confirmation_number</mat-icon>
                    <span class="text-gray-700">My Tickets</span>
                  </button>

                  <button mat-button class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                          (click)="navigateTo('/settings/profile')">
                    <mat-icon class="!mr-3 text-gray-500">account_box</mat-icon>
                    <span class="text-gray-700">Account Settings</span>
                  </button>

                  <button mat-button class="w-full !justify-start !px-3 !py-2 !rounded-lg hover:!bg-gray-50"
                          (click)="navigateTo('/help')">
                    <mat-icon class="!mr-3 text-gray-500">help</mat-icon>
                    <span class="text-gray-700">Help Center</span>
                  </button>
                </div>

                <mat-divider class="my-4"></mat-divider>

                <button mat-flat-button color="warn" class="w-full !py-2 !px-4 !rounded-lg" (click)="logout()">
                  <mat-icon class="!mr-2">logout</mat-icon>
                  Sign Out
                </button>
              </div>
            </mat-menu>
          </div>
        </div>
      </div>

      <!-- Mobile Search Bar -->
      <div *ngIf="showMobileSearch" class="p-4 border-t md:hidden">
        <div class="relative">
          <mat-icon class="absolute left-3 top-2.5 text-gray-400">search</mat-icon>
          <input type="text" placeholder="Search..." class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
      </div>
    </header>
  `,
  styles: [`
    :host ::ng-deep {
      .notifications-menu {
        border-radius: 0.75rem !important;
        margin-top: 0.5rem !important;
        overflow: hidden !important;
      }

      .profile-menu {
        margin-top: 0.5rem !important;

        .mat-mdc-menu-content {
          padding: 0 !important;
        }
      }

      .mat-mdc-menu-panel {
        min-width: unset !important;
      }

      .mat-mdc-menu-item {
        min-height: 40px !important;
        line-height: 40px !important;
        font-size: 14px !important;

        .mat-icon {
          margin-right: 12px !important;
        }

        &:hover {
          background-color: #F3F4F6 !important;
        }
      }

      .mat-mdc-button {
        height: 44px !important;
      }

      .mat-mdc-flat-button {
        height: 44px !important;
      }
    }
  `]
})
export class HeaderComponent implements OnInit {
  @Output() menuToggle = new EventEmitter<void>();

  showMobileSearch = false;
  notifications = [
    {
      title: 'New Ticket Assigned',
      message: 'You have been assigned to ticket TICK-123',
      time: '5 minutes ago',
      icon: 'assignment',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600'
    },
    {
      title: 'Sprint Review',
      message: 'Sprint review meeting starts in 30 minutes',
      time: '30 minutes ago',
      icon: 'event',
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600'
    },
    {
      title: 'System Update',
      message: 'System maintenance scheduled for tonight',
      time: '2 hours ago',
      icon: 'update',
      iconBg: 'bg-yellow-100',
      iconColor: 'text-yellow-600'
    }
  ];

  profileData = {
    fullName: '',
    email: '',
    roleName: '',
    initials: ''
  };

  constructor(
    private router: Router,
    private authService: AuthService,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('userId'); // Ensure this is set during login
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
  
    this.http.get<any>(`http://localhost:3000/api/ProfileMenu/profile/${userId}`, { headers }).subscribe({
      next: (res) => {
        this.profileData = res;
      },
      error: (err) => {
        console.error('Failed to load profile:', err);
      }
    });
  }

   getProfileImage(size: number = 32): string {
    const initials = this.profileData.initials || 'U';
    return `https://placehold.co/${size}x${size}/6366F1/ffffff?text=${initials}`;
   }

  onMenuToggle(): void {
    this.menuToggle.emit();
  }

  toggleMobileSearch(): void {
    this.showMobileSearch = !this.showMobileSearch;
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  viewAllNotifications(): void {
    this.router.navigate(['/notifications']);
  }

  logout(): void {
    this.authService.logout();
  }
}
