import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Menu } from '../Models/Menu.model';

@Injectable({
  providedIn: 'root'
})
export class MenuService {
  private apiUrl = 'http://localhost:3000/api/menu/getMenuByRole';

  constructor(private http: HttpClient) {}

  getMenuItems(roleId: number): Observable<Menu[]> {
     return this.http.get<Menu[]>(`${this.apiUrl}/${roleId}`);
    // return this.http.get<Menu[]>(`${this.apiUrl}getByRoleId/${roleId}`);
  }
}
