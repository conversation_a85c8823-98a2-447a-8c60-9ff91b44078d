import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { BehaviorSubject, Observable, throwError, of } from "rxjs";
import { catchError, tap, map, finalize } from "rxjs/operators";

//export type TicketType = 'Bug' | 'Feature' | 'Task' | 'Improvement';
//export type TicketStatus = 'New' | 'In Progress' | 'Resolved' | 'Closed';
//export type TicketPriority = 'Low' | 'Medium' | 'High' | 'Critical';

export interface TicketType {
  ticketTypeId: number;
  ticketTypeName: string;
}

export interface TicketPriority {
  priorityId: number;
  priorityName: string;
}

export interface TicketStatus {
  statusid: number;
  statusname: string;
}

export interface Assignee {
  userId: number;
  firstName: string;
  lastName: string;
}

export interface Sprint {
  sprint_id: number;
  sprint_name: string;
}

export interface Attachment {
  attachment_id: number;
  file_name: string;
  file_url: string;
  uploaded_at: string;
}

export interface Tag {
  tag_id: number;
  tag_name: string;
}

export interface Ticket {
  ticket_id: number;
  title: string;
  description: string;
  type_id: number;
  tickettypename: string;
  priority_id: number;
  priorityname: string;
  statusid: number;
  statusname: string;
  userid: number;
  firstname: string;
  sprint_id: number;
  sprint_name: string;
  estimated_time: number;
  last_updated: string;
  tags: { tag_id: number; tag_name: string }[];
  attachments: {
    attachment_id: number;
    file_name: string;
    file_url: string;
    uploaded_at: string;
  }[];
  existingAttachments?: Attachment[]; // Optional for editing a ticket (metadata for existing attachments)
}

export interface TicketFilters {
  search: string;
  status: string[];
  priority: string[];
  sprint: string[];
}

// export interface Comment {
//   commentId: number;
//   ticketId: number;
//   userId: number;
//   parentCommentId: number | null;
//   commentText: string | null;
//   createdAt: Date;
//   firstName?: string;
//   replies?: Comment[];
// }

export interface Comment {
  commentId: number;
  ticketId: number;
  userId: number;
  parentCommentId?: number;
  commentText: string | null;
  createdAt: Date;
  firstName?: string;
  attachments?: {
    fileName: string;
    fileUrl: string;
  }[];
  replies?: Comment[];
}

@Injectable({
  providedIn: "root",
})
export class TicketService {
  private apiUrl = "http://localhost:3000/api";
  private tickets = new BehaviorSubject<Ticket[]>([]);
  private loading = new BehaviorSubject<boolean>(false);
  private error = new BehaviorSubject<string | null>(null);
  private filters = new BehaviorSubject<TicketFilters>({
    search: "",
    status: [],
    priority: [],
    sprint: [],
  });

  
  addComment(payload: {
    ticketId: number;
    userId: number;
    commentText: string;
    parentCommentId?: number;
  }) {
    return this.http.post(`${this.apiUrl}/Comments/add`, payload); 
  }
  uploadAttachmentsWithComment(formData: FormData): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/Comments/upload-with-comment`, formData);
  }
  
  
  // Fetch comments for a specific ticket
  getComments(ticketId: number): Observable<Comment[]> {
    return this.http.get<Comment[]>(`${this.apiUrl}/Comments/GetCommentsByTicketId/${ticketId}`);
  }

  updateTicketsState(updatedTickets: Ticket[]): void {
    this.tickets.next(updatedTickets);
  }
  
  getTicketsObservable(): Observable<Ticket[]> {
    return this.tickets.asObservable(); // ✅ Expose tickets safely
  }
  constructor(private http: HttpClient) {
    this.loadTickets();
  }
  createTag(tagName: string): Observable<Tag> {
    return this.http.post<Tag>("api/tags", { tag_name: tagName });
  }
  getTicketTypes(): Observable<TicketType[]> {
    return this.http.get<TicketType[]>(
      `${this.apiUrl}/TicketType/AllTicketTypes`
    );
  }

  getTags(): Observable<Tag[]> {
    return this.http.get<Tag[]>(`${this.apiUrl}/Tags/AllTags`); 
  }
  getAllTicketStatus(): Observable<TicketStatus[]> {
    return this.http.get<TicketStatus[]>(`${this.apiUrl}/Status/AllStatus`);
  }

  getAllPriorities(): Observable<TicketPriority[]> {
    return this.http.get<TicketPriority[]>(
      `${this.apiUrl}/Priority/AllPriority`
    );
  }

  createTicketWithAttachments(formData: FormData): Observable<any> {
    console.log("Create ticket inputs received:", formData);

    return this.http.post<any>(`${this.apiUrl}/Ticket/CreateTicket`, formData);
  }

  updateTicketWithAttachments(
    ticketid: number,
    formData: FormData
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/Ticket/EditTicket/${ticketid}`,
      formData
    );
  }

  private loadTickets() {
    this.loading.next(true);
    this.error.next(null);

    this.http
      .get<Ticket[]>(`${this.apiUrl}/Ticket/AllTicket`)
      .pipe(finalize(() => this.loading.next(false)))
      .subscribe({
        next: (tickets) => {
          console.log("Tickets received:", tickets);
          this.tickets.next(tickets);
          this.error.next(null);
        },
        error: (err) => {
          console.error("Error loading tickets:", err);
          this.error.next("Failed to load tickets");
          this.tickets.next([]);
        },
      });
  }

  // getTickets(): Observable<Ticket[]> {
  //   // return this.tickets.asObservable();

  //   return this.http.get<Ticket[]>(`${this.apiUrl}/Ticket/AllTicket`);
  // }

  getTickets(): Observable<Ticket[]> {
    return this.http.get<any[]>(`${this.apiUrl}/Ticket/AllTicket`).pipe(
      map((response) =>
        response.map((ticket) => ({
          ticket_id: ticket.ticket_id,
          title: ticket.title,
          description: ticket.description,
          type_id: ticket.type_id,
          tickettypename: ticket.tickettypename,
          priority_id: ticket.priority_id,
          priorityname: ticket.priorityname,
          statusid: ticket.statusid,
          statusname: ticket.statusname,
          sprint_id: ticket.sprint_id,
          sprint_name: ticket.sprint_name,
          estimated_time: ticket.estimated_time,
          last_updated: ticket.last_updated,
          id: ticket.ticket_id?.toString() || "",
          firstname: ticket.firstname || "",
          userid: ticket.userid ?? 0, // Ensure 'userid' is mapped correctly
          assignedTo: ticket.userid ? ticket.userid.toString() : "",
          type: {
            ticketTypeId: ticket.type_id,
            ticketTypeName: ticket.tickettypename,
          },
          priority: {
            priorityId: ticket.priority_id,
            priorityName: ticket.priorityname,
          },
          status: {
            statusid: ticket.statusid,
            statusname: ticket.statusname,
          },
          sprint: {
            sprint_id: ticket.sprint_id,
            sprint_name: ticket.sprint_name,
          },
          tags: Array.isArray(ticket.tags)
            ? ticket.tags.map((tag: any) => ({
                tag_id: tag.tag_id,
                tag_name: tag.tag_name,
              }))
            : [],
          attachments: Array.isArray(ticket.attachments)
            ? ticket.attachments.map((attachment: any) => ({
                name: attachment.file_name,
                url: attachment.file_url,
              }))
            : [],
          estimatedTime: ticket.estimated_time,
          lastUpdated: ticket.last_updated,
        }))
      ),
      tap((tickets) => {
        this.tickets.next(tickets); // ✅ Updates BehaviorSubject after fetching tickets
      })
    );
  }

  isLoading(): Observable<boolean> {
    return this.loading.asObservable();
  }

  getError(): Observable<string | null> {
    return this.error.asObservable();
  }

  getTicketById(id: number): Observable<Ticket | null> {
    this.loading.next(true);
    this.error.next(null);

    return this.http
      .get<Ticket>(`${this.apiUrl}/Ticket/GetTicketById/${id}`)
      .pipe(
        map((ticket) => ticket),
        catchError((error) => {
          console.error("Error fetching ticket:", error);
          this.error.next(`Failed to fetch ticket ${id}`);
          return of(null);
        }),
        finalize(() => this.loading.next(false))
      );
  }

  updateTicket(id: number, updates: Partial<Ticket>): Observable<Ticket> {
    this.loading.next(true);
    this.error.next(null);

    return this.http.put<Ticket>(`${this.apiUrl}/tickets/${id}`, updates).pipe(
      tap((updatedTicket) => {
        const currentTickets = this.tickets.value;
        const index = currentTickets.findIndex((t) => t.ticket_id === id);
        if (index !== -1) {
          currentTickets[index] = updatedTicket;
          this.tickets.next([...currentTickets]);
        }
        this.error.next(null);
      }),
      catchError((error) => {
        console.error("Error updating ticket:", error);
        this.error.next(`Failed to update ticket ${id}`);
        return throwError(() => error);
      }),
      finalize(() => this.loading.next(false))
    );
  }

  deleteTicket(id: number): Observable<void> {
    this.loading.next(true);
    this.error.next(null);

    return this.http
      .delete<void>(`${this.apiUrl}/Ticket/DeleteTicket/${id}`)
      .pipe(
        tap(() => {
          const updatedTickets = this.tickets.value.filter(
            (t) => t.ticket_id !== id
          );
          this.tickets.next([...updatedTickets]); // ✅ Ensure change detection
          this.error.next(null);
        }),
        catchError((error) => {
          console.error("Error deleting ticket:", error);
          this.error.next(`Failed to delete ticket ${id}`);
          return throwError(() => error);
        }),
        finalize(() => this.loading.next(false))
      );
  }

  getTeamMembers(): Observable<Assignee[]> {
    return this.http.get<Assignee[]>(`${this.apiUrl}/user/AllAssignee`).pipe(
      catchError((error) => {
        console.error("Error fetching assignee", error);
        return throwError(() => error);
      })
    );
  }

  getSprints(): Observable<Sprint[]> {
    return this.http.get<Sprint[]>(`${this.apiUrl}/Sprint/AllSprint`).pipe(
      catchError((error) => {
        console.error("Error fetching sprints:", error);
        return throwError(() => error);
      })
    );
  }

  setFilters(filters: Partial<TicketFilters>) {
    this.filters.next({ ...this.filters.value, ...filters });
  }

  getFilters(): Observable<TicketFilters> {
    return this.filters.asObservable();
  }

  clearError() {
    this.error.next(null);
  }
}
