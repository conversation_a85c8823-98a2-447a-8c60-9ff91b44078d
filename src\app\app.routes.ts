import { Routes } from "@angular/router";
import { AuthGuard } from "./guards/auth.guard";
import { AuthLayout } from "./layouts/auth.layout";
import { MainLayout } from "./layouts/main.layout";

import { StatusCreateComponent } from "./pages/settings/project-settings/status/status-create/status-create.component";
import { RoleCreateComponent } from "./pages/settings/project-settings/roles/role-create/role-create.component";

export const routes: Routes = [
  {
    path: "auth",
    component: AuthLayout,
    children: [
      {
        path: "login",
        loadComponent: () =>
          import("./pages/auth/login/login.component").then(
            (m) => m.LoginComponent
          ),
      },
      {
        path: "register",
        loadComponent: () =>
          import("./pages/auth/register/register.component").then(
            (m) => m.RegisterComponent
          ),
      },
    ],
  },
  {
    path: "",
    component: MainLayout,
    canActivate: [AuthGuard],
    children: [
      { path: "", redirectTo: "dashboard", pathMatch: "full" },
      {
        path: "dashboard",
        loadComponent: () =>
          import("./pages/dashboard/dashboard.component").then(
            (m) => m.DashboardComponent
          ),
      },
      {
        path: "tickets",
        children: [
          {
            path: "list",
            loadComponent: () =>
              import("./pages/tickets/ticket-list/ticket-list.component").then(
                (m) => m.TicketListComponent
              ),
          },
          {
            path: "create",
            loadComponent: () =>
              import(
                "./pages/tickets/ticket-create/ticket-create.component"
              ).then((m) => m.TicketCreateComponent),
            data: { role: "admin" },
          },
          {
            path: "details",
            loadComponent: () =>
              import(
                "./pages/tickets/ticket-details/ticket-details.component"
              ).then((m) => m.TicketDetailsComponent),
          },
        ],
      },
      {
        path: "sprint",
        loadComponent: () =>
          import("./pages/sprint/sprint.component").then(
            (m) => m.SprintComponent
          ),
      },

      {
        path: "sprints/edit/:id",
        loadComponent: () =>
          import("./pages/sprint/create-sprint/create-sprint.component").then(
            (m) => m.CreateSprintComponent
          ),
        data: { role: "admin" },
      },
      {
        path: "sprints/create",
        loadComponent: () =>
          import("./pages/sprint/create-sprint/create-sprint.component").then(
            (m) => m.CreateSprintComponent
          ),
      },

      {
        path: "backlog",
        loadComponent: () =>
          import("./pages/backlog/backlog.component").then(
            (m) => m.BacklogComponent
          ),
      },
      {
        path: "users",
        loadComponent: () =>
          import("./pages/users/users.component").then((m) => m.UsersComponent),
        data: { role: "admin" },
      },
      {
        path: "reports",
        children: [
          {
            path: "sprint",
            loadComponent: () =>
              import(
                "./pages/reports/sprint-reports/sprint-reports.component"
              ).then((m) => m.SprintReportsComponent),
          },
          {
            path: "velocity",
            loadComponent: () =>
              import("./pages/reports/velocity/velocity.component").then(
                (m) => m.VelocityComponent
              ),
          },
          {
            path: "burndown",
            loadComponent: () =>
              import("./pages/reports/burndown/burndown.component").then(
                (m) => m.BurndownComponent
              ),
          },
        ],
      },
      {
        path: "notifications",
        loadComponent: () =>
          import("./pages/notifications/notifications.component").then(
            (m) => m.NotificationsComponent
          ),
      },
      {
        path: "settings",
        children: [
          {
            path: "project",

            data: { role: "admin" },
            children: [
              {
                path: "",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/project-settings.component"
                  ).then((m) => m.ProjectSettingsComponent),
              },
              {
                path: "status",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/status/status-list/status-list.component"
                  ).then((m) => m.StatusListComponent),
              },
              {
                path: "status/status-edit/:id",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/status/status-create/status-create.component"
                  ).then((m) => StatusCreateComponent),
                
              },
              {
                path: "status/status-create",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/status/status-create/status-create.component"
                  ).then((m) => m.StatusCreateComponent),
              },

 {
                path: "roles",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/roles/role-list/role-list.component"
                  ).then((m) => m.RoleListComponent),
              },
              {
                path: "roles/roles-edit/:id",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/roles/role-create/role-create.component"
                  ).then((m) => RoleCreateComponent),
                
              },
              {
                path: "roles/roles-create",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/roles/role-create/role-create.component"
                  ).then((m) => m.RoleCreateComponent),
              },

               {
                path: "tag",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/tag/list-tag/list-tag.component"
                  ).then((m) => m.ListTagComponent),
              },
              {
                path: "tag/create-tag/:id",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/tag/create-tag/create-tag.component"
                  ).then((m) => m.CreateTagComponent),
                
              },
              {
                path: "tag/create-tag",
                loadComponent: () =>
                  import(
                    "./pages/settings/project-settings/tag/create-tag/create-tag.component"
                  ).then((m) => m.CreateTagComponent),
              },


            ],
          },
          {
            path: "workflow",
            loadComponent: () =>
              import("./pages/settings/workflow/workflow.component").then(
                (m) => m.WorkflowComponent
              ),
            data: { role: "admin" },
          },
          {
            path: "integrations",
            loadComponent: () =>
              import(
                "./pages/settings/integrations/integrations.component"
              ).then((m) => m.IntegrationsComponent),
            data: { role: "admin" },
          },
        ],
      },
    ],
  },
  { path: "**", redirectTo: "auth/login" },
];
