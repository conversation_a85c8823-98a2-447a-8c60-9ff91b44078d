import { bootstrapApplication } from '@angular/platform-browser';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { App } from '../app/app.component';
import { routes } from '../app/app.routes';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { PickerModule } from '@ctrl/ngx-emoji-mart';

bootstrapApplication(App, {
  providers: [
    provideHttpClient(withInterceptors([AuthInterceptor])),
    provideAnimations(),
    provideRouter(routes)
  ]
}).catch(err => console.error(err));
