import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

import { MenuItemComponent } from './menu-item.component';
import { Menu } from '../../Models/Menu.model';

@Component({
  selector: 'app-sidebar-menu',
  standalone: true,
  imports: [CommonModule, MatIconModule, MenuItemComponent],
  template: `
    <div class="flex flex-col h-full bg-white shadow-lg">
      <!-- Logo Header -->
      <div class="flex-none p-4 border-b">
        <div class="flex items-center">
          <img src="https://placehold.co/32x32/4F46E5/ffffff?text=S" alt="Logo" class="h-8 w-8 mr-2 rounded">
          <span class="text-xl font-semibold">SprintTrack</span>
        </div>
      </div>
      
      <!-- Navigation Menu -->
      <nav class="flex-1 overflow-y-auto py-2">
        <div class="space-y-0.5">
          <app-menu-item *ngFor="let item of menuItems" [item]="item"></app-menu-item>
        </div>
      </nav>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      height: 100%;
      width: 256px;
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    ::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    nav {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
    }
  `]
})
// export class SidebarMenuComponent {
//   @Input() menuItems: MenuItemComponent[] | null = [];
// }
export class SidebarMenuComponent {
  @Input() menuItems: Menu[] | null = []; // ✅ Use Menu[] instead of MenuItemComponent[]
}