import { Component,OnInit } from '@angular/core';
import { TagService} from '../../../../../services/tag.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-create-tag',
  imports: [ReactiveFormsModule,MatIconModule,CommonModule],
  templateUrl: './create-tag.component.html',
  styleUrl: './create-tag.component.css'
})
export class CreateTagComponent implements OnInit {
   tagForm: FormGroup;
  isEdit = false;
  tagId?: number;

  constructor(
    private fb: FormBuilder,
    private tagService: TagService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.tagForm = this.fb.group({
      tag_name: ['', Validators.required],
      isactive: [true]  // default value is true
    });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.tagId = params['id'];
      if (this.tagId) {
        this.isEdit = true;
        this.tagService.getTagById(this.tagId).subscribe(tag => {
          this.tagForm.patchValue(tag);
        });
      }
    });
  }

  

  onSubmit(): void {
    if (this.tagForm.invalid) {
      this.tagForm.get('tag_name')?.markAsTouched();
      return;
    }

    const tagData = this.tagForm.value;

    if (this.isEdit && this.tagId) {
      this.tagService.updateTag(this.tagId, tagData).subscribe(
        () => {
          console.log('Tag updated successfully');
          this.router.navigate(['/settings/project/tag']);
        },
        (error) => {
          console.error('Error updating tag:', error);
          if (error.status === 409) {
            this.tagForm.get('tag_name')?.setErrors({ duplicate: true });
          }
        }
      );
    } else {
      this.tagService.createTag(tagData).subscribe(
        () => {
          console.log('Tag created successfully');
          this.router.navigate(['/settings/project/tag']);
        },
        (error) => {
          console.error('Error creating tag:', error);
          if (error.status === 409) {
            this.tagForm.get('tag_name')?.setErrors({ duplicate: true });
          }
        }
      );
    }
  }

  goBack(): void {
    this.router.navigate(['/settings/project/tag']);
  }
}