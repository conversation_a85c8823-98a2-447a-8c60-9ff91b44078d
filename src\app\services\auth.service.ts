import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roleId: number; // ✅ Ensure roleId is used
  token: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = 'http://localhost:3000/api/auth/'; // Ensure this matches your backend

  

  private currentUserSubject = new BehaviorSubject<User | null>(this.getStoredUser());
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient, private router: Router) {}

  /**
   * Retrieve stored user from localStorage
   */
  private getStoredUser(): User | null {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        return user.token ? user : null;
      } catch {
        return null;
      }
    }
    return null;
  }

  public get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Login User
   */
  login(email: string, password: string): Observable<User> {
    return this.http.post<User>(`${this.apiUrl}login`, { email, password })
      .pipe(
        tap(user => {
          console.log('user', user);
          console.log('user Role', user.roleId);
          localStorage.setItem('currentUser', JSON.stringify(user));
          localStorage.setItem('role', user.roleId.toString());  // ✅ Store roleId explicitly
          
          this.currentUserSubject.next(user);
        }),
        catchError(error => throwError(() => new Error(error.error?.message || 'Login failed')))
      );
  }



  register(user: Partial<User>): Observable<User> {
    return this.http.post<User>(`http://localhost:3000/api/register/add`, user);
  }

  /**
   * Logout User
   */
  logout(): void {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('role'); // ✅ Clear role
    this.currentUserSubject.next(null);
    this.router.navigate(['/auth/login'], { replaceUrl: true });
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.currentUser?.token;
  }


  /**
   * Check if user has a specific role
   */
  hasRole(roleId: number): boolean {  
    return this.currentUser?.roleId === roleId;
  }
}
