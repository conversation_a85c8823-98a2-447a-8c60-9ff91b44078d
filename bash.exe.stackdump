Stack trace:
Frame         Function      Args
0007FFFF9F10  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E10) msys-2.0.dll+0x1FE8E
0007FFFF9F10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1E8) msys-2.0.dll+0x67F9
0007FFFF9F10  000210046832 (000210286019, 0007FFFF9DC8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F10  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F10  000210068E24 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA1F0  00021006A225 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC872A0000 ntdll.dll
7FFC86810000 KERNEL32.DLL
7FFC84830000 KERNELBASE.dll
7FFC85F10000 USER32.dll
7FFC84F70000 win32u.dll
7FFC86630000 GDI32.dll
7FFC84C20000 gdi32full.dll
7FFC84780000 msvcp_win.dll
7FFC84630000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC855F0000 advapi32.dll
7FFC85720000 msvcrt.dll
7FFC86760000 sechost.dll
7FFC857D0000 RPCRT4.dll
7FFC839F0000 CRYPTBASE.DLL
7FFC84FA0000 bcryptPrimitives.dll
7FFC87220000 IMM32.DLL
