<!-- <p>status-list works!</p> -->
<div class="bg-white rounded-xl shadow-sm overflow-hidden mx-20 mt-12">
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Status</h3>
        <button 
          mat-raised-button 
          (click)="navigateToCreate()"  
          class="!bg-blue-600 !text-white hover:!bg-blue-700 !px-4 !py-2 !rounded-lg !font-medium flex items-center m-2">
          <mat-icon class="!mr-2">add</mat-icon>
          Add Status
        </button>
        
      </div>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
           

            <th class="px-6 py-3 text-left text-xs font-extrabold text-gray-900 uppercase tracking-wider">STATUS NAME</th>
             
              
              <th class="px-6 py-3 text-left text-xs font-extrabold text-gray-900 uppercase tracking-wider">ACTIONS</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let status of paginatedStatuses">               
           
            <td class="px-6 py-4 whitespace-nowrap">
              <span  class="px-2.5 py-1 text-xs font-medium rounded-full">
                {{ status.statusname }}
              </span>

              
              </td>
            
            <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
              <button mat-icon-button 
                        [matMenuTriggerFor]="actionMenu"
                        class="!text-gray-400 font-bold">
                  <mat-icon class="font-bold">more_vert</mat-icon>
                </button>
                <mat-menu #actionMenu="matMenu">
                  <button mat-menu-item (click)="viewStatus(status)" class="menu-item">
                    <mat-icon>visibility</mat-icon>
                    <span>View</span>
                  </button>
                  <button mat-menu-item (click)="editStatus(status)">
                    <mat-icon>edit</mat-icon>
                    <span>Edit</span>
                  </button>
                  
                <button mat-menu-item (click)="deleteStatus(status.statusid)" class="menu-item text-red-600">
                  <mat-icon>delete</mat-icon>
                  <span>Remove</span>
                </button>
                </mat-menu>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  


    
  </div>
 <div class="bg-white rounded-xl px-6 py-4  flex items-center justify-between  mx-20 text-gray-900  mt-6">
      <div class="flex items-center space-x-2">
          <span class="text-gray-600">Show</span>
          <select class="pagination-select border-gray-300 rounded-md px-2 py-1 text-sm text-gray-900"
                  [(ngModel)]="pageSize"
                  (change)="onPageSizeChange()">
              <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }} items</option>
          </select>
      </div>
      <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-700">
              {{ startIndex + 1 }} - {{ endIndex }} of {{ totalRecords }}
          </div>
          <button mat-icon-button (click)="firstPage()" [disabled]="currentPage === 0">
              <mat-icon>first_page</mat-icon>
          </button>
          <button mat-icon-button (click)="previousPage()" [disabled]="currentPage === 0">
              <mat-icon>chevron_left</mat-icon>
          </button>
          <div class="flex items-center space-x-1">
              <ng-container *ngFor="let page of visiblePages">
                  <button *ngIf="page !== '...'"
                          class="pagination-number rounded-md px-2 py-1 text-sm"
                          [class.active]="currentPage + 1 === page"
                          (click)="goToPage(+page - 1)">
                      {{ page }}
                  </button>
                  <span *ngIf="page === '...'" class="text-gray-500">...</span>
              </ng-container>
          </div>
          <button mat-icon-button (click)="nextPage()" [disabled]="currentPage === totalPages - 1">
              <mat-icon>chevron_right</mat-icon>
          </button>
          <button mat-icon-button (click)="lastPage()" [disabled]="currentPage === totalPages - 1">
              <mat-icon>last_page</mat-icon>
          </button>
      </div> 