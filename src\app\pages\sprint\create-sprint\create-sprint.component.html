<div class="scroll-container">
    <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ isEditMode ? 'Edit' : 'Create' }} Sprint
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEditMode ? 'Update the sprint details below' : 'Fill in the form to create a new sprint' }}
          </p>
        </div>
        <!-- <button mat-button (click)="goBack()" class="text-gray-600 hover:text-gray-900">
          <mat-icon class="!mr-2">arrow_back</mat-icon> Back to List
        </button> -->
        <!-- <button mat-button (click)="goBack()" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
          <mat-icon class="text-lg">arrow_back</mat-icon>
          <span>Back to List</span>
        </button> -->
        <button mat-button (click)="goBack()" class="back-button">
          <mat-icon class="text-lg">arrow_back</mat-icon>
          <span>Back to List</span>
        </button>
        
        
        
      </div>
  
      <!-- Form -->
      <form [formGroup]="sprintForm" (ngSubmit)="onSubmit()" class="p-6 form-container">
        <!-- Sprint Name -->
        <div class="form-group">
          <label class="form-label">Sprint Name</label>
          <input type="text" formControlName="sprint_name" class="form-input" placeholder="Enter sprint name">
          <div *ngIf="sprintForm.get('sprint_name')?.touched && sprintForm.get('sprint_name')?.invalid" class="error-message">
            Sprint name is required
          </div>
        </div>
  
        <!-- Dates -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="form-group">
            <label class="form-label">Start Date</label>
            <input type="date" formControlName="start_date" class="form-input">
          </div>
        
          <div class="form-group">
            <label class="form-label">End Date</label>
            <input type="date" formControlName="end_date" class="form-input">
        
            <div *ngIf="sprintForm.get('end_date')?.errors?.['invalidEndDate'] && sprintForm.get('end_date')?.touched" 
                 class="text-red-500 text-sm mt-1">
              End Date cannot be before Start Date.
            </div>
          </div>
        </div>
        
          <!-- <div class="form-group">
            <label class="form-label">End Date</label>
            <input type="date" formControlName="end_date" class="form-input">
          </div> 
        </div>-->
  
        <!-- Sprint Goal -->
        <div class="form-group">
          <label class="form-label">Sprint Goal</label>
          <textarea formControlName="sprint_goal" rows="3" class="form-input" placeholder="Enter sprint goal"></textarea>
          <div *ngIf="sprintForm.get('sprint_goal')?.touched && sprintForm.get('sprint_goal')?.invalid" class="error-message">
            Sprint goal is required
          </div>
        </div>
  
        <!-- Status Dropdown -->
        <!-- <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status_name" class="form-input">
              <option value="" disabled>Select status</option>
              <option *ngFor="let status of statusOptions" [value]="status.status_name">
                {{ status.status_name }}
              </option>
            </select>
            <div *ngIf="sprintForm.get('status_name')?.touched && sprintForm.get('status_name')?.invalid" class="error-message">
              Status is required
            </div>
          </div> -->
          

          <div class="form-group w-1/2">
            <label class="form-label">Status</label>
            <select formControlName="status_name" class="form-input">
              <option value="" disabled>Select status</option>
              <option *ngFor="let status of statusOptions" [value]="status.status_name">
                {{ status.status_name }}
              </option>
            </select>
            <div *ngIf="sprintForm.get('status_name')?.touched && sprintForm.get('status_name')?.invalid" class="error-message">
              Status is required
            </div>
          </div>
          
  
        <!-- Submit Button -->
        <!-- <div class="submit-container">
          <button mat-raised-button color="primary" type="submit" [disabled]="sprintForm.invalid">
            {{ isEditMode ? 'Update' : 'Create' }} Sprint
          </button>
        </div> -->

        <!-- Submit Button Container -->
<!-- Submit Button Container -->
<!-- <div class="submit-container">
  <button mat-raised-button color="primary" type="submit" [disabled]="sprintForm.invalid">
    <span class="flex items-center">
      <mat-icon class="!mr-2">{{ isEditMode ? 'update' : 'add' }}</mat-icon>
      {{ isEditMode ? 'Update' : 'Create' }} Sprint
    </span>
  </button>
</div> -->
<div class="submit-container">
  <button
    mat-raised-button
    type="submit"
    [disabled]="sprintForm.invalid"
    [ngClass]="{ 'btn-valid': sprintForm.valid }"
  >
    <span class="flex items-center">
      <mat-icon class="!mr-2">{{ isEditMode ? 'update' : 'add' }}</mat-icon>
      {{ isEditMode ? 'Update' : 'Create' }} Sprint
    </span>
  </button>
</div>



      </form>
    </div>
  </div>
  