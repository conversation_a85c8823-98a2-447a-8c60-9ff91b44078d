/* Base Font Setup */
body, table, th, td, .pagination-container, .pagination-number, .pagination-arrow {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
}

.container {
  max-width: 1500px;
  width: 100%;
  margin: 0 auto;
  background: #f8f9fb;
  padding: 24px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  width: 100%;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

/* Add Button */
.add-btn {
  background-color: #1d4ed8;
  color: #fff;
  border: none;
  padding: 10px 20px;
  font-size: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
}
.add-btn:hover {
  background-color: #1e40af;
  transform: scale(1.05);
}

/* List Tags Section */
.list-tags {
  margin-top: 0;
}

table.curved-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* clean, modern sans-serif */
  font-size: 14px; /* base font size for table body */
  background-color: #f9fafb;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  table-layout: fixed;
}

/* Table Header */
th {
  font-size: 13px;
 color: #6b7280; /* Deep blue-gray color matching the image */
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.06em;
  padding: 14px;
  background-color: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  text-align: left;
 
}

/* Table Body */
td {
  background-color: #ffffff;
  padding: 14px;
  border-bottom: 1px solid #e5e7eb;
  color: #34495e;               /* Medium blue-gray for body text */
  font-weight: 400;             /* Normal */
  font-size: 14.5px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

tbody tr:hover {
  background-color: #f1f5f9;
}


/* Action Button (three-dot menu) */
/* .action-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
} */


/* Pagination Styling */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 25px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-select {
  padding: 6px 12px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 14px;
  max-height: 150px;
  overflow-y: auto;
  width: 120px;
}

.pagination-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: flex-end;
  width: 100%;
}

.pagination-info {
  font-size: 14px;
  color: #555;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

/* Pagination Number */
.pagination-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 12px;
  border-radius: 0.5rem;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  background-color: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.pagination-number:hover {
  background-color: #f1f5f9;
}

.pagination-number.active {
  background-color: #eff6ff;
  color: #2563eb;
  border-color: #bfdbfe;
}

/* Pagination Arrows */
.pagination-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: auto;
  border-radius: 50%;
  background-color: transparent;
  color: #555;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 10px;
  border: none;
  font-size: 20px;
}

/* Hover and Active States for Arrows */
.pagination-arrow:hover {
  background-color: #e2e8f0;
  border-radius: 8px;
}

.pagination-arrow.active {
  background-color: #eff6ff;
  color: #2563eb;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

/* Center the Action column (both header and data cell) */
th:last-child,
td:last-child {
  text-align: center;
  vertical-align: middle; /* Vertically align content */
  white-space: nowrap; /* Prevent text from wrapping */
}

/* Center Action button */
td:last-child .action-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.status-pill {
  display: inline-block;
  padding: 4px 12px;       /* Slightly smaller padding for oval shape */
  font-size: 13px;         /* Match font size from the image */
  font-weight: 600;        /* Slightly bolder for clarity */
  border-radius: 9999px;   /* Fully rounded pill */
  text-align: center;
  min-width: 60px;         /* Adjust width to match oval size */
  text-transform: capitalize;
  transition: all 0.2s ease-in-out;
  line-height: 1.2;
  letter-spacing: 0.4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.07);
  border: none;
}

/* Green (active) - precise colors from typical priority tags */
.status-pill.active {
  background-color: #dafbe1; /* softer green background */
  color: #238636;            /* richer green text */
}

/* Red (inactive) - exact tone from the image */
.status-pill.inactive {
  background-color: #fee2e2; /* soft red background */
  color: #b32626;            /* strong red text */
}

/* You can keep other statuses as is or tweak similarly */
.status-pill.pending {
  background-color: #fefce8;
  color: #92400e;
}

.status-pill.suspended {
  background-color: #f0f9ff;
  color: #1e3a8a;
}



/* delete-confirmation-dialog.component.css */
:host {
  display: block;
  font-family: 'Inter', sans-serif;
}

/* Force remove dialog default padding */
:host ::ng-deep .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 12px;
  box-shadow: 0px 5px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 480px !important;
  height: 340px !important; /* Adjust height to match your screenshot */
  box-sizing: border-box;
}

.p-8 {
  padding: 24px;
  background-color: #ffffff;
  height: 100%;
  box-sizing: border-box;
}

.text-2xl {
  font-size: 20px;
  font-weight: 300;
}

.text-gray-900 {
  color: #1f2937;
}

.text-gray-600 {
  color: #4b5563;
  font-size: 15px;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded-xl {
  border-radius: 12px;
}

.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.hover\:border-red-200:hover {
  border-color: #fecaca;
}

.transition-colors {
  transition: all 0.2s ease-in-out;
}

.duration-200 {
  transition-duration: 200ms;
}

.text-sm {
  font-size: 14px;
}

.text-gray-500 {
  color: #6b7280;
}

.font-medium {
  font-weight: 500;
}

.pl-6 {
  padding-left: 24px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-8 {
  margin-bottom: 32px;
}

.space-x-4 > :not(:last-child) {
  margin-right: 16px;
}

.w-12 {
  width: 48px;
}

.h-12 {
  height: 48px;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-600 {
  color: #dc2626;
}

.text-white {
  color: white;
}

.bg-red-600 {
  background-color: #dc2626;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.rounded-lg {
  border-radius: 8px;
}

.px-6 {
  padding-left: 24px;
  padding-right: 24px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.transition-colors {
  transition: all 0.2s;
}

.duration-200 {
  transition-duration: 200ms;
}

.text-gray-700 {
  color: #374151;
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
}

.mr-4 {
  margin-right: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.border-4 {
  border-width: 4px;
}

.rounded-full {
  border-radius: 9999px;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-ping {
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(1.5);
    opacity: 0;
  }
}


/* Action buttons */
/* Action buttons */
.edit-btn {
  background-color: #1d4ed8; /* Dark Blue */
  color: white;
  border: none;
  padding: 4px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  margin-right: 6px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: background-color 0.3s ease;
}
.edit-btn:hover {
  background-color: #1e40af; /* Darker Blue on hover */
}

.delete-btn {
  background-color: #dc2626; /* Dark Red */
  color: white;
  border: none;
  padding: 4px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: background-color 0.3s ease;
}
.delete-btn:hover {
  background-color: #991b1b; /* Darker Red on hover */
}

/* Disabled Pagination Button Style */
button[disabled],
button:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: auto; /* ensures cursor shows even if disabled */
}