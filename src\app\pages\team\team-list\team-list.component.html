<div class="container">
  <!-- Header -->
  <div class="header">
    <h1 class="text-2xl font-semibold text-gray-900">Teams</h1>
    <button class="add-btn" (click)="navigateToCreate()">+ Add Team</button>
  </div>

  <!-- Team Table -->
  <div class="list-tags">
    <table class="curved-table">
      <thead>
        <tr>
          <th>Team Name</th>
          <th>Description</th>
          <th class="text-right">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let team of paginatedTeams">
          <td>{{ team.teamName }}</td>
          <td>{{ team.description }}</td>
          <td class="text-right">
            <button mat-icon-button [matMenuTriggerFor]="menu" class="action-btn">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu" class="menu-panel">
              <button mat-menu-item (click)="navigateToEdit(team)">
                <mat-icon>edit</mat-icon>
                <span>Edit</span>
              </button>
            </mat-menu>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
<div class="pagination-container">
  <div class="pagination-left">
    <span>Show</span>
    <select class="form-select" [(ngModel)]="pageSize" (change)="onPageSizeChange()">
      <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }} items</option>
    </select>
  </div>

  <div class="pagination-right">
    <span class="pagination-info">
      {{ (currentPage * pageSize) + 1 }} -
      {{ Math.min((currentPage + 1) * pageSize, totalRecords) }}
      of {{ totalRecords }}
    </span>

    <div class="pagination-buttons">
      <!-- First -->
      <button (click)="goToFirstPage()" class="pagination-arrow" title="First Page" [disabled]="currentPage === 0">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2"
             viewBox="0 0 24 24">
          <line x1="6" y1="6" x2="6" y2="18"></line>
          <polyline points="18 6 12 12 18 18"></polyline>
        </svg>
      </button>

      <!-- Prev -->
      <button (click)="goToPreviousPage()" class="pagination-arrow" title="Previous Page" [disabled]="currentPage === 0">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2"
             viewBox="0 0 24 24">
          <path d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <!-- Page Numbers -->
      <button
        *ngFor="let page of getPageNumbers(); let i = index"
        (click)="onPageChange(page)"
        [class.active]="page === currentPage"
        class="pagination-number">
        {{ page + 1 }}
      </button>

      <!-- Next -->
      <button (click)="goToNextPage()" class="pagination-arrow" title="Next Page" [disabled]="currentPage >= totalPages - 1">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2"
             viewBox="0 0 24 24">
          <path d="M9 5l7 7-7 7"></path>
        </svg>
      </button>

      <!-- Last -->
      <button (click)="goToLastPage()" class="pagination-arrow" title="Last Page" [disabled]="currentPage >= totalPages - 1">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2"
             viewBox="0 0 24 24">
          <line x1="18" y1="6" x2="18" y2="18"></line>
          <polyline points="6 6 12 12 6 18"></polyline>
        </svg>
      </button>
    </div>
  </div>
</div>

