import { HttpClientModule } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { MenuService } from '../../services/menu.service';
import { ToastrService } from 'ngx-toastr';
import { Menu } from '../../Models/Menu.model';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [HttpClientModule, RouterModule, CommonModule], 
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.css']
})
export class MenuComponent implements OnInit {
  userRoleId: number | null = null; 
  menuItems: Menu[] = [];
  mainMenus: Menu[] = [];
  activeDropdown: number | null = null; 

  constructor(private menuService: MenuService, private alert: ToastrService) {}

  ngOnInit(): void {
    this.userRoleId = this.getUserRole();
    this.loadMenuByRole(this.userRoleId);
  }

  getUserRole(): number {
    const roleIdString = localStorage.getItem('role');
    if (roleIdString) {
      const roleId = parseInt(roleIdString, 10);
      return isNaN(roleId) ? 1 : roleId;
    }
    return 1;
  }

  loadMenuByRole(roleId: number): void {
    this.menuService.getMenuItems(roleId).subscribe(
      (data: Menu[]) => {
        this.menuItems = data;
        this.mainMenus = this.menuItems.filter(menu => menu.parentId === 0);

        this.mainMenus.forEach(menu => {
          menu.children = this.getSubMenus(menu.menuId);
        });
      },
      error => {
        this.alert.warning('Error fetching menus:', error.message);
      }
    );
  }

  getSubMenus(parentId: number): Menu[] {
    const subMenus = this.menuItems.filter(menu => menu.parentId === parentId);
    
    subMenus.forEach(subMenu => {
      subMenu.children = this.getSubMenus(subMenu.menuId);
    });

    return subMenus;
  }

  toggleDropdown(menuId: number): void {
    const menuHasChildren = this.mainMenus.find(menu => menu.menuId === menuId)?.children?.length;
    if (menuHasChildren) {
      this.activeDropdown = this.activeDropdown === menuId ? null : menuId;
    }
  }
}
