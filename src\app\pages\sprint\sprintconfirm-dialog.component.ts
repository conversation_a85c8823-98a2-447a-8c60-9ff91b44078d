import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-sprint-confirm-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, MatIconModule],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.95)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'scale(1)' }))
      ])
    ])
  ],
  template: `
  <div class="dialog-container" [@fadeIn]>
  <!-- HEADER -->
    <div class="flex items-center mb-6">
    <div class="w-12 h-12 rounded-full flex items-center justify-center mr-3 relative"
     [ngClass]="data.error ? 'bg-yellow-100' : 'bg-red-100'">
  
  <!-- Animate Ping Ripple -->
  <div
    class="absolute inset-0 rounded-full border-4 animate-ping opacity-75"
    [ngClass]="data.error ? 'border-yellow-200' : 'border-red-200'">
  </div>

  <!-- Icon -->
  <mat-icon
    [ngClass]="data.error ? 'text-yellow-600' : 'text-red-600'"
    class="text-xl z-10">
    {{ data.error ? 'info' : 'warning' }}
  </mat-icon>
</div>


    <h2 class="text-2xl font-bold text-gray-900">
      {{ data.error ? 'Cannot Delete Sprint' : 'Delete Sprint' }}
    </h2>
  </div>

  <!-- MESSAGE FIRST -->
  <ng-container *ngIf="!data.error; else errorTemplate">
    <p class="text-gray-600 mb-4 text-sm leading-relaxed">
      Are you sure you want to delete this sprint? This action cannot be undone.
    </p>

    <!-- SPRINT DETAILS -->
    <div class="bg-gray-50 rounded-xl p-4 mb-6 border border-gray-200 text-xs">
      <div class="text-gray-500 mb-1">Sprint ID: {{ data.id || 'N/A' }}</div>
      <div class="text-gray-900 font-medium pl-2">{{ data.name || 'N/A' }}</div>
    </div>

    <div class="button-group flex justify-end space-x-3">
      <button
        mat-button
        (click)="onCancel()"
        class="btn btn-cancel"
        id="btnCancelSprintDialog"
        name="btnCancelSprintDialog"
      >
        <mat-icon class="mr-1 text-sm">close</mat-icon> Cancel
      </button>

      <button
        *ngIf="data.showConfirm"
        mat-flat-button
        (click)="onConfirm()"
        class="btn btn-delete"
        id="btnConfirmSprintDialog"
        name="btnConfirmSprintDialog"
      >
        <mat-icon class="mr-1 text-sm">delete</mat-icon> Delete
      </button>
    </div>
  </ng-container>

  <!-- ERROR TEMPLATE -->
  <ng-template #errorTemplate>
    <p class="text-gray-600 mb-4 text-sm leading-relaxed">
      This sprint cannot be deleted because it is assigned to one or more tickets.
    </p>

    <!-- SPRINT DETAILS -->
    <div class="bg-gray-50 rounded-xl p-4 mb-6 border border-gray-200 text-xs">
      <div class="text-gray-500 mb-1">Sprint ID: {{ data.id || 'N/A' }}</div>
      <div class="text-gray-900 font-medium pl-2">{{ data.name || 'N/A' }}</div>
    </div>

    <div class="flex justify-end">
      <button
        mat-flat-button
        (click)="onCancel()"
        class="btn btn-cancel"
        id="btnCloseSprintDialog"
        name="btnCloseSprintDialog"
      >
        <mat-icon class="mr-1 text-sm">close</mat-icon> Close
      </button>
    </div>
  </ng-template>
</div>

  `,
  styles: [
    `
      /* Remove box shadow from dialog container */
      .dialog-container {
        padding: 1.5rem;
        width: 100%;
        height:360px;
        background-color: white;
        border-radius: 1rem;
        border: 1px solid #e5e7eb;
        box-shadow: none !important; /* Important to override */
      }
        .dialog-container h2 {
  font-size: 1.75rem !important; /* text-lg */
}

.bg-gray-50 .text-gray-500,
.bg-gray-50 .text-gray-900 {
  font-size: 0.95rem !important; /* text-xs */
}

      /* Strong overrides for Angular Material buttons */
      .btn {
        box-shadow: none !important;
        border-radius: 0 !important; /* Sharp corners */
        padding-top: 4px !important;
        padding-bottom: 4px !important;
        font-size: 0.875rem !important; /* text-sm */
        min-width: 80px !important;
        text-transform: none !important;
        cursor: pointer;
        border-width: 1px !important;
        border-style: solid !important;
        transition: background-color 0.3s ease, border-color 0.3s ease;
      }

      /* Cancel and Close buttons styling */
      .btn-cancel {
        background-color: #f3f4f6 !important; /* gray-100 */
        color: #374151 !important; /* gray-700 */
        border-color: #d1d5db !important; /* gray-300 */
      }

      .btn-cancel:hover {
        background-color: #e5e7eb !important; /* gray-200 */
        border-color: #9ca3af !important; /* darker gray */
      }

      /* Delete button styling */
      .btn-delete {
        background-color: #dc2626 !important; /* red-600 */
        color: white !important;
        border: none !important;
      }

      .btn-delete:hover {
        background-color: #b91c1c !important; /* red-700 */
      }

      
    .animate-pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: .7;
      }
    }

    .animate-ping {
      animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
    }

    @keyframes ping {
      75%, 100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }


    `
  ]
})
export class SprintConfirmDialogComponent {
  constructor(
    private dialogRef: MatDialogRef<SprintConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      id: number;
      name: string;
      error: boolean;
      showConfirm?: boolean;
    }
  ) {}

  onConfirm() {
    this.dialogRef.close(true);
  }

  onCancel() {
    this.dialogRef.close(false);
  }
}
