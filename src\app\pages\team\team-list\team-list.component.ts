import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TeamService } from '../../../services/team.service';
import { Team } from '../../../Models/Team.model';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';

@Component({
  standalone: true,
  selector: 'app-team-list',
  templateUrl: './team-list.component.html',
  styleUrl: './team-list.component.css',
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatMenuModule,
    MatIconModule,
    MatButtonModule
  ]
})
export class TeamListComponent implements OnInit {
  teams: Team[] = [];
  paginatedTeams: Team[] = [];
  Math = Math;

  // Pagination
  currentPage = 0;
  pageSize = 5;
  totalRecords = 100;
  pageSizeOptions = [5, 10, 20, 50];

  constructor(private teamService: TeamService, private router: Router) {}

  ngOnInit(): void {
    this.loadTeams();
  }

  // Method to load teams data
  loadTeams(): void {
    this.teamService.getAllTeams().subscribe((data: Team[]) => {
      this.teams = data;
      this.totalRecords = this.teams.length;
      this.updatePaginatedTeams();
    });
  }

  // Pagination logic
  updatePaginatedTeams(): void {
    const start = this.currentPage * this.pageSize;
    const end = start + this.pageSize;
    this.paginatedTeams = this.teams.slice(start, end);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updatePaginatedTeams();
  }

  goToPreviousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updatePaginatedTeams();
    }
  }

  goToNextPage(): void {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
      this.updatePaginatedTeams();
    }
  }

  goToFirstPage(): void {
    this.currentPage = 0;
    this.updatePaginatedTeams();
  }

  goToLastPage(): void {
    this.currentPage = this.totalPages - 1;
    this.updatePaginatedTeams();
  }

  onPageSizeChange(): void {
    this.currentPage = 0;
    this.updatePaginatedTeams();
  }

  // Method to get the total number of pages
  get totalPages(): number {
    return Math.ceil(this.totalRecords / this.pageSize);
  }

  // Method to get visible page numbers for pagination
  getPageNumbers(): number[] {
    const visiblePages = 5;
    let start = Math.max(0, this.currentPage - Math.floor(visiblePages / 2));
    let end = start + visiblePages;
    if (end > this.totalPages) {
      end = this.totalPages;
      start = Math.max(0, end - visiblePages);
    }
    return Array.from({ length: end - start }, (_, i) => start + i);
  }

  // Navigation methods
  navigateToCreate(): void {
    this.router.navigate(['settings/project/teams/create']);
  }

  navigateToEdit(team: Team): void {
    this.router.navigate(['/settings/project/team/edit', team.teamId]);
  }
}
