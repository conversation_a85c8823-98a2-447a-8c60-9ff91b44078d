import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../../services/auth.service';

@Component({
    selector: 'app-register',
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatInputModule,
        MatIconModule
    ],
    template: `
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <img src="https://placehold.co/64x64/6366F1/ffffff?text=D" 
             alt="Logo" 
             class="mx-auto h-16 w-16 rounded">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <a routerLink="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
            sign in to your existing account
          </a>
        </p>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- Error Message -->
            <div *ngIf="error" 
                 class="p-4 bg-red-50 border border-red-200 rounded-md">
              <p class="text-sm text-red-600">{{error}}</p>
            </div>

            <!-- Name Fields -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <!-- First Name -->
              <div>
                <label for="firstName" class="block text-sm font-medium text-gray-700">
                  First name
                </label>
                <div class="mt-1">
                  <input id="firstName"
                         type="text"
                         formControlName="firstName"
                         class="form-input"
                         [class.error]="isFieldInvalid('firstName')"
                         placeholder="Enter your first name">
                  <div *ngIf="isFieldInvalid('firstName')" class="error-message">
                    First name is required
                  </div>
                </div>
              </div>

              <!-- Last Name -->
              <div>
                <label for="lastName" class="block text-sm font-medium text-gray-700">
                  Last name
                </label>
                <div class="mt-1">
                  <input id="lastName"
                         type="text"
                         formControlName="lastName"
                         class="form-input"
                         [class.error]="isFieldInvalid('lastName')"
                         placeholder="Enter your last name">
                  <div *ngIf="isFieldInvalid('lastName')" class="error-message">
                    Last name is required
                  </div>
                </div>
              </div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div class="mt-1">
                <input id="email"
                       type="email"
                       formControlName="email"
                       class="form-input"
                       [class.error]="isFieldInvalid('email')"
                       placeholder="Enter your email">
                <div *ngIf="isFieldInvalid('email')" class="error-message">
                  Please enter a valid email address
                </div>
              </div>
            </div>

            <!-- Password -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div class="mt-1 relative">
                <input [type]="showPassword ? 'text' : 'password'"
                       id="password"
                       formControlName="password"
                       class="form-input pr-10"
                       [class.error]="isFieldInvalid('password')"
                       placeholder="Create a password">
                <button type="button"
                        (click)="togglePasswordVisibility()"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <mat-icon class="text-gray-400">
                    {{showPassword ? 'visibility_off' : 'visibility'}}
                  </mat-icon>
                </button>
                <div *ngIf="isFieldInvalid('password')" class="error-message">
                  Password must be at least 8 characters
                </div>
              </div>
            </div>

            <!-- Confirm Password -->
            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
                Confirm password
              </label>
              <div class="mt-1 relative">
                <input [type]="showConfirmPassword ? 'text' : 'password'"
                       id="confirmPassword"
                       formControlName="confirmPassword"
                       class="form-input pr-10"
                       [class.error]="isFieldInvalid('confirmPassword')"
                       placeholder="Confirm your password">
                <button type="button"
                        (click)="toggleConfirmPasswordVisibility()"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <mat-icon class="text-gray-400">
                    {{showConfirmPassword ? 'visibility_off' : 'visibility'}}
                  </mat-icon>
                </button>
                <div *ngIf="isFieldInvalid('confirmPassword')" class="error-message">
                  Passwords do not match
                </div>
              </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="flex items-center">
              <input type="checkbox"
                     id="terms"
                     formControlName="terms"
                     class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="terms" class="ml-2 block text-sm text-gray-900">
                I agree to the
                <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a>
                and
                <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
              </label>
            </div>

            <!-- Submit Button -->
            <div>
              <button type="submit"
                      [disabled]="registerForm.invalid || isLoading"
                      class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <mat-icon *ngIf="isLoading" class="animate-spin mr-2">refresh</mat-icon>
                {{isLoading ? 'Creating account...' : 'Create account'}}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `
})
export class RegisterComponent {
  registerForm: FormGroup;
  isLoading = false;
  error = '';
  showPassword = false;
  showConfirmPassword = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.registerForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', Validators.required],
      terms: [false, Validators.requiredTrue]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(g: FormGroup) {
    return g.get('password')?.value === g.get('confirmPassword')?.value
      ? null
      : { mismatch: true };
  }

  isFieldInvalid(field: string): boolean {
    const formControl = this.registerForm.get(field);
    return !!formControl && formControl.invalid && formControl.touched;
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true;
      this.error = '';

      const { confirmPassword, terms, ...userData } = this.registerForm.value;

      this.authService.register(userData).subscribe({
        next: () => {
          this.router.navigate(['/auth/login'], {
            queryParams: { registered: true }
          });
        },
        error: (error) => {
          this.error = error.error?.message || 'Registration failed. Please try again.';
          this.isLoading = false;
        }
      });
    } else {
      Object.keys(this.registerForm.controls).forEach(key => {
        const control = this.registerForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
    }
  }
}