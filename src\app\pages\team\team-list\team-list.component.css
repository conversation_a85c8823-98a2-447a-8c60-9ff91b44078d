.container {
  width: 100%;

  margin: 0 auto;
  padding: 1rem;
}

/* Header Block */
.header {
   width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

/* Table Block */
.list-tags {
  width: 100%;
  background-color: #ffffff;

  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.curved-table {
  width: 100%;
  border-collapse: collapse;
}

.curved-table th,
.curved-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  text-align: left;
}

.curved-table th {
  background-color: #f9fafb;
  font-weight: 600;
}

/* Pagination Block */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Add Button Styling */
.add-btn {
  background-color: #2563eb;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #1d4ed8;
}

/* Pagination Numbers */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  background-color: #ffffff;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  gap: 1rem;
}

/* Ensure the left and right sections stay inline */
.pagination-left,
.pagination-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Select dropdown style */
.form-select {
  padding: 0.4rem 0.6rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

/* Pagination controls aligned in one row */
.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.apagination-buttons:hover {
  background-color: #1d4ed8;
}

.pagination-info {
  margin-right: 1rem;
  white-space: nowrap;
}

.pagination-number,
.pagination-arrow {
  padding: 0.3rem 0.6rem;
  border: none;
  border-radius: 6px;
  background-color: #f3f4f6;
  cursor: pointer;
  font-size: 0.95rem;
}

.pagination-number.active {
 
  color:black;
}








.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
  margin-top: 24px;
  flex-wrap: wrap;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-left select {
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 14px;
}

.pagination-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.pagination-info {
  font-size: 14px;
  color: #555;
}

.pagination-buttons {
  display: flex;
  gap: 6px;
}

.pagination-arrow,
.pagination-number {
  border: none;
  background: transparent;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: 0.2s ease;
  font-size: 14px;
  color: #333;
}

.pagination-arrow:disabled,
.pagination-number:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination-number.active {
  background-color: #e5f0ff;
  border: 1px solid #007bff;
  font-weight: bold;
  color: #007bff;
}

