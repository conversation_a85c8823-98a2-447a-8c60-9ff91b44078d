<div class="scroll-container">
  <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ isEditMode ? 'Edit' : 'Create' }} Role
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEditMode ? 'Update the role information below' : 'Fill in the information below to create a new role'
            }}
          </p>
        </div>
        <button mat-button (click)="goBack()" class="back-button">
          <mat-icon class="text-lg">arrow_back</mat-icon>
          <span>Back to List</span>
        </button>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="roleForm" (ngSubmit)="onSubmit()" class="p-6 form-container">
      <div class="form-group">
        <label class="form-label">Role Name</label>
        <input type="text" formControlName="roleName" class="form-input" placeholder="Enter Role Name"
          />

        <div *ngIf="roleForm.get('roleName')?.touched && roleForm.get('roleName')?.invalid" class="error-message">
          Role Name is required
        </div>
        <div *ngIf="roleNameExists" class="error-message">
          This role name already exists.
        </div>
      </div>
 <!-- Status Toggle -->
     <label class="form-label flex items-center gap-2 cursor-pointer">
  Status
  <input
    type="checkbox"
    formControlName="isActive"
    id="isActive"
    class="h-5 w-5 text-blue-600 accent-blue-600 ml-4"
  />
</label>

      <!-- Button Container for Submit -->
      <div class="button-container">
        <button type="submit" [disabled]="roleForm.invalid || roleNameExists" class="right-button">
          <mat-icon *ngIf="isEditMode">update</mat-icon>
          <mat-icon *ngIf="!isEditMode">add</mat-icon>
          {{ isEditMode ? ' Update Role' : ' Create Role' }}
        </button>
      </div>
    </form>
  </div>
</div>
