import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { Task } from '../services/task.service';

@Component({
  selector: 'app-task-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule
  ],
  template: `
    <div class="p-6">
      <h2 class="text-2xl font-semibold mb-4">{{ data.task ? 'Edit Task' : 'Create Task' }}</h2>
      <form #taskForm="ngForm" (ngSubmit)="onSubmit()">
        <div class="space-y-4">
          <mat-form-field class="w-full" appearance="outline">
            <mat-label>Title</mat-label>
            <input matInput [(ngModel)]="task.title" name="title" required>
          </mat-form-field>

          <mat-form-field class="w-full" appearance="outline">
            <mat-label>Description</mat-label>
            <textarea matInput [(ngModel)]="task.description" name="description" rows="3"></textarea>
          </mat-form-field>

          <mat-form-field class="w-full" appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select [(ngModel)]="task.status" name="status" required>
              <mat-option value="TODO">To Do</mat-option>
              <mat-option value="IN_PROGRESS">In Progress</mat-option>
              <mat-option value="DONE">Done</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="w-full" appearance="outline">
            <mat-label>Priority</mat-label>
            <mat-select [(ngModel)]="task.priority" name="priority" required>
              <mat-option value="LOW">Low</mat-option>
              <mat-option value="MEDIUM">Medium</mat-option>
              <mat-option value="HIGH">High</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="w-full" appearance="outline">
            <mat-label>Assignee</mat-label>
            <input matInput [(ngModel)]="task.assignee" name="assignee">
          </mat-form-field>
        </div>

        <div class="flex justify-end space-x-2 mt-6">
          <button mat-button type="button" (click)="onCancel()">Cancel</button>
          <button mat-raised-button color="primary" type="submit" [disabled]="!taskForm.valid">
            {{ data.task ? 'Update' : 'Create' }}
          </button>
        </div>
      </form>
    </div>
  `
})
export class TaskDialogComponent {
  task: Partial<Task>;

  constructor(
    public dialogRef: MatDialogRef<TaskDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { task?: Task }
  ) {
    this.task = data.task ? { ...data.task } : {
      title: '',
      description: '',
      status: 'TODO',
      priority: 'MEDIUM',
      assignee: ''
    };
  }

  onSubmit(): void {
    this.dialogRef.close(this.task);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}