<div class="scroll-container">
    <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
      
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ isEditMode ? 'Edit' : 'Create' }} Team
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEditMode ? 'Update the team details below' : 'Fill in the form to create a new team' }}
          </p>
        </div>
        
        <button mat-button (click)="goBack()" class="back-button">
          <mat-icon class="text-lg">arrow_back</mat-icon>
          <span>Back to List</span>
        </button>
      </div>
  
      <!-- Form -->
      <form [formGroup]="teamForm" (ngSubmit)="onSubmit()" class="p-6 form-container">
        
        <!-- Team Name -->
        <div class="form-group">
          <label class="form-label">Team Name</label>
          <input type="text" formControlName="team_name" class="form-input" placeholder="Enter team name">
          <div *ngIf="teamForm.get('team_name')?.touched && teamForm.get('team_name')?.invalid" class="error-message">
            Team name is required
          </div>
        </div>
  
        <!-- Description -->
        <div class="form-group">
          <label class="form-label">Description</label>
          <textarea formControlName="description" rows="3" class="form-input" placeholder="Enter team description"></textarea>
          <div *ngIf="teamForm.get('description')?.touched && teamForm.get('description')?.invalid" class="error-message">
            Description is required
          </div>
        </div>
  
        <!-- Submit Button -->
        <!-- <div class="submit-container">
          <button mat-raised-button color="primary" type="submit" [disabled]="teamForm.invalid">
            <span class="flex items-center">
              <mat-icon class="!mr-2">{{ isEditMode ? 'update' : 'add' }}</mat-icon>
              {{ isEditMode ? 'Update' : 'Create' }} Team
            </span>
          </button>
        </div> -->

        <div class="submit-container">
  <button mat-raised-button
          type="submit"
          [disabled]="teamForm.invalid"
          [ngClass]="{'valid-button': teamForm.valid, 'invalid-button': teamForm.invalid}">
    <span class="flex items-center">
      <mat-icon class="!mr-2">{{ isEditMode ? 'update' : 'add' }}</mat-icon>
      {{ isEditMode ? 'Update' : 'Create' }} Team
    </span>
  </button>
</div>

  
      </form>
    </div>
  </div>
  
