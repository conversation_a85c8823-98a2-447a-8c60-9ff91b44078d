.container {
    padding: 20px;
    background-color: #f9fafb;
  } 
  .status-name-container {
    display: inline;
    padding: 0;
    border-radius: 0;
  
  }
  
  /* Header styles */
   h2{
    font-weight: bold;
    margin-bottom: 20px;
    font-size: 24px;
    text-align: left;
    padding: 20px;
    margin: 5px;
    background-color: white;
  
  } 
  
  /* Table styles */
  .table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 20px;
  }
  
  .table thead {
    background-color: #f3f4f6;
  }
  
  .table th,
  .table td {
    padding: 16px;
    text-align: left;
    font-size: 16px;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .table td:last-child {
    text-align: left;
   
  }
  
  .table tbody tr:hover {
    background-color: #f9fafb;
  }
  /* Action buttons styles */
  button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: large;
  }
  
  button.btn-warning {
    background-color: green;
    color: white;
    transition: background-color 0.3s;
  }
  
  button.btn-warning:hover {
    background-color: blue;
  }
  
  button.btn-danger {
    background-color: #ef4444;
    color: white;
    transition: background-color 0.3s;
  }
  
  button.btn-danger:hover {
    background-color: #dc2626;
  }
  
  .status-name-container {
    display: flex;
    flex-direction: column;
    padding: 8px;
  }
  
  .status-label {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    color: #333;
  } 
  
   
  
  /* Responsive design for smaller screens */
  @media screen and (max-width: 768px) {
    .table th, .table td {
      font-size: 12px;
      padding: 8px;
    }
  
    button {
  
      font-size: 12px;
      padding: 6px 12px;
    }
  }
  
  @media screen and (min-width: 769px) and (max-width: 1440px) {
    .table th, .table td {
      font-size: 14px;
      padding: 10px;
    }
  
    button {
      font-size: 14px;
      padding: 8px 16px;
    }
    
  }
  
  
  